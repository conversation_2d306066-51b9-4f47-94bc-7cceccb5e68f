<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('workspace_id'); // No foreign key constraint due to pivot relationship
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->decimal('accuracy', 8, 2)->nullable(); // GPS accuracy in meters
            $table->decimal('altitude', 8, 2)->nullable(); // Altitude in meters
            $table->decimal('heading', 5, 2)->nullable(); // Direction in degrees
            $table->decimal('speed', 8, 2)->nullable(); // Speed in m/s
            $table->string('address')->nullable(); // Reverse geocoded address
            $table->json('metadata')->nullable(); // Additional location metadata
            $table->boolean('is_active')->default(true); // Current location flag
            $table->timestamp('recorded_at'); // When location was recorded
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'recorded_at']);
            $table->index(['workspace_id', 'recorded_at']);
            $table->index(['latitude', 'longitude']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_locations');
    }
};
