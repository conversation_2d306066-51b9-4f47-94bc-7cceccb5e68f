/*
 * NestKo Responsive Design Implementation
 * Mobile-first approach with breakpoints:
 * - Mobile: ≤ 480px
 * - Tablet: 481px–1024px (portrait & landscape)
 * - Desktop: ≥ 1025px
 */

/* ==========================================================================
   Base Mobile-First Styles (≤ 480px)
   ========================================================================== */

/* NestKo Feature Cards */
.nestko-feature-card {
    margin-bottom: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nestko-feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Mobile Map Container */
.nestko-map-container {
    position: relative;
    width: 100%;
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
}

.nestko-map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.nestko-map-controls .btn {
    padding: 8px;
    min-width: 40px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Mobile Chat Interface */
.nestko-chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 600px;
}

.nestko-chat-header {
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.nestko-chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: #f8f9fa;
    max-height: 400px;
}

.nestko-chat-input {
    padding: 1rem;
    background: white;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e9ecef;
}

/* Mobile Message Bubbles */
.nestko-message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.nestko-message.user {
    flex-direction: row-reverse;
}

.nestko-message-bubble {
    max-width: 85%;
    padding: 0.75rem 1rem;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
}

.nestko-message.user .nestko-message-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 6px;
}

.nestko-message.assistant .nestko-message-bubble {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 6px;
}

.nestko-message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

/* Mobile User Location Markers */
.nestko-user-marker {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid #28a745;
    background-size: cover;
    background-position: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.nestko-user-marker:hover {
    transform: scale(1.1);
    border-width: 4px;
}

.nestko-user-marker.stale {
    border-color: #ffc107;
    opacity: 0.8;
}

/* Mobile Team Members List */
.nestko-team-member {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.nestko-team-member:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.nestko-team-member-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 0.75rem;
    object-fit: cover;
}

.nestko-team-member-info {
    flex: 1;
}

.nestko-team-member-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #333;
}

.nestko-team-member-status {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Mobile Geofence List */
.nestko-geofence-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.nestko-geofence-item.alert-enter {
    border-left-color: #28a745;
}

.nestko-geofence-item.alert-exit {
    border-left-color: #ffc107;
}

.nestko-geofence-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.nestko-geofence-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

/* Mobile Quick Actions */
.nestko-quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.nestko-quick-action {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nestko-quick-action:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
}

.nestko-quick-action i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

/* Mobile Floating Action Button */
.nestko-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    z-index: 1050;
    transition: all 0.3s ease;
}

.nestko-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* Mobile Collapsible Panels */
.nestko-collapsible {
    background: white;
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nestko-collapsible-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.nestko-collapsible-content {
    padding: 1rem;
    display: none;
}

.nestko-collapsible.active .nestko-collapsible-content {
    display: block;
}

.nestko-collapsible.active .nestko-collapsible-header i {
    transform: rotate(180deg);
}

/* Mobile Touch Interactions */
.nestko-touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile Loading States */
.nestko-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6c757d;
}

.nestko-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: nestko-spin 1s linear infinite;
    margin-right: 0.75rem;
}

@keyframes nestko-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Status Badges */
.nestko-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nestko-status-badge.live {
    background: #d4edda;
    color: #155724;
}

.nestko-status-badge.stale {
    background: #fff3cd;
    color: #856404;
}

.nestko-status-badge.offline {
    background: #f8d7da;
    color: #721c24;
}

/* Mobile Swipe Gestures */
.nestko-swipeable {
    position: relative;
    overflow: hidden;
}

.nestko-swipe-actions {
    position: absolute;
    top: 0;
    right: -100px;
    height: 100%;
    width: 100px;
    background: #dc3545;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: right 0.3s ease;
}

.nestko-swipeable.swiped .nestko-swipe-actions {
    right: 0;
}

/* ==========================================================================
   Tablet Styles (481px–1024px)
   ========================================================================== */

@media (min-width: 481px) and (max-width: 1024px) {
    
    /* Tablet Map Container */
    .nestko-map-container {
        height: 400px;
    }
    
    /* Tablet Chat Interface */
    .nestko-chat-container {
        max-height: 700px;
    }
    
    .nestko-chat-messages {
        max-height: 500px;
    }
    
    /* Tablet Message Bubbles */
    .nestko-message-bubble {
        max-width: 75%;
    }
    
    /* Tablet Quick Actions Grid */
    .nestko-quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    /* Tablet Team Members - Two Column Layout */
    .nestko-team-members-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    /* Tablet Geofence List - Two Column Layout */
    .nestko-geofences-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    /* Tablet Floating Action Button */
    .nestko-fab {
        bottom: 30px;
        right: 30px;
        width: 64px;
        height: 64px;
        font-size: 1.75rem;
    }
    
    /* Tablet Touch Targets */
    .nestko-touch-target {
        min-height: 48px;
        min-width: 48px;
    }
    
    /* Tablet Landscape Specific */
    @media (orientation: landscape) {
        .nestko-chat-container {
            flex-direction: row;
            height: 100vh;
            max-height: none;
        }
        
        .nestko-chat-messages {
            max-height: none;
        }
        
        .nestko-map-container {
            height: 350px;
        }
    }
}

/* ==========================================================================
   Desktop Styles (≥ 1025px)
   ========================================================================== */

@media (min-width: 1025px) {
    
    /* Desktop Map Container */
    .nestko-map-container {
        height: 500px;
    }
    
    /* Desktop Chat Interface */
    .nestko-chat-container {
        max-height: 800px;
    }
    
    .nestko-chat-messages {
        max-height: 600px;
    }
    
    /* Desktop Message Bubbles */
    .nestko-message-bubble {
        max-width: 65%;
    }
    
    /* Desktop Quick Actions Grid */
    .nestko-quick-actions {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    
    /* Desktop Team Members - Three Column Layout */
    .nestko-team-members-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    
    /* Desktop Geofence List - Three Column Layout */
    .nestko-geofences-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    
    /* Desktop Hover Effects */
    .nestko-feature-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }
    
    .nestko-quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    /* Desktop Floating Action Button - Hide on Desktop */
    .nestko-fab {
        display: none;
    }
    
    /* Desktop Touch Targets */
    .nestko-touch-target {
        min-height: 32px;
        min-width: 32px;
    }
}

/* ==========================================================================
   Accessibility & High Contrast
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .nestko-feature-card,
    .nestko-quick-action,
    .nestko-user-marker,
    .nestko-fab {
        transition: none;
    }
    
    .nestko-loading-spinner {
        animation: none;
    }
}

@media (prefers-contrast: high) {
    .nestko-message.user .nestko-message-bubble {
        background: #000;
        color: #fff;
    }
    
    .nestko-message.assistant .nestko-message-bubble {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
    
    .nestko-status-badge.live {
        background: #000;
        color: #fff;
    }
}

/* ==========================================================================
   Mobile-Specific Enhancements
   ========================================================================== */

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    .nestko-chat-input input {
        font-size: 16px !important; /* Prevent zoom on focus */
    }

    .nestko-touch-target {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
}

/* Android Chrome specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .nestko-message-bubble {
        word-break: break-word;
        overflow-wrap: break-word;
    }
}

/* Landscape mobile optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .nestko-chat-container {
        height: 100vh;
    }

    .nestko-chat-messages {
        max-height: 250px;
    }

    .nestko-fab {
        bottom: 10px;
        right: 10px;
        width: 48px;
        height: 48px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .nestko-user-marker {
        border-width: 2px;
    }

    .nestko-feature-card {
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .nestko-feature-card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .nestko-message.assistant .nestko-message-bubble {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }

    .nestko-collapsible-header {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }

    .nestko-quick-action {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }

    .nestko-quick-action:hover {
        background: #2d3748;
        border-color: #667eea;
        color: #667eea;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .nestko-feature-card,
    .nestko-quick-action,
    .nestko-user-marker,
    .nestko-fab,
    .nestko-message {
        transition: none !important;
        animation: none !important;
    }

    .nestko-loading-spinner {
        animation: none !important;
    }

    * {
        scroll-behavior: auto !important;
    }
}

/* Focus management for accessibility */
.nestko-touch-target:focus,
.nestko-quick-action:focus,
.nestko-fab:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Improved contrast for accessibility */
@media (prefers-contrast: high) {
    .nestko-message.user .nestko-message-bubble {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }

    .nestko-message.assistant .nestko-message-bubble {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }

    .nestko-status-badge.live {
        background: #000;
        color: #fff;
        border: 1px solid #fff;
    }

    .nestko-status-badge.stale {
        background: #fff;
        color: #000;
        border: 1px solid #000;
    }

    .nestko-fab {
        background: #000 !important;
        color: #fff;
        border: 2px solid #fff;
    }
}

/* Smooth scrolling for supported browsers */
@supports (scroll-behavior: smooth) {
    .nestko-chat-messages {
        scroll-behavior: smooth;
    }
}

/* CSS Grid fallback for older browsers */
@supports not (display: grid) {
    .nestko-quick-actions {
        display: flex;
        flex-direction: column;
    }

    .nestko-team-members-grid,
    .nestko-geofences-grid {
        display: block;
    }

    .nestko-team-member,
    .nestko-geofence-item {
        margin-bottom: 1rem;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .nestko-fab,
    .nestko-map-controls,
    .nestko-chat-input,
    .nestko-collapsible-header {
        display: none !important;
    }

    .nestko-map-container {
        height: 300px;
        border: 1px solid #000;
    }

    .nestko-feature-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
        margin-bottom: 1rem;
    }

    .nestko-collapsible-content {
        display: block !important;
    }

    .nestko-message {
        break-inside: avoid;
        margin-bottom: 0.5rem;
    }

    .nestko-message-bubble {
        border: 1px solid #000;
        background: #fff !important;
        color: #000 !important;
    }
}
