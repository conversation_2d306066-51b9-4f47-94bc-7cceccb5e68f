@extends('layout')

@section('title')
    {{ get_label('geolocation_map', 'Geolocation Map') }}
@endsection

@section('styles')
<link rel="stylesheet" href="{{ asset('assets/css/nestko-responsive.css') }}">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="fw-bold py-3 mb-4">
                <span class="text-muted fw-light">{{ get_label('nestko', 'NestKo') }} /</span>
                {{ get_label('geolocation_map', 'Geolocation Map') }}
            </h4>
        </div>
        <div>
            <button type="button" class="btn btn-primary" id="refreshLocations">
                <i class="bx bx-refresh"></i> {{ get_label('refresh', 'Refresh') }}
            </button>
            <button type="button" class="btn btn-outline-primary" id="myLocationBtn">
                <i class="bx bx-current-location"></i> {{ get_label('my_location', 'My Location') }}
            </button>
        </div>
    </div>

    <!-- Feature Check -->
    @if(!isNestkoFeatureEnabled('geolocation'))
        <div class="alert alert-warning">
            <i class="bx bx-info-circle"></i>
            {{ get_label('geolocation_feature_disabled', 'Geolocation feature is not enabled. Please contact your administrator.') }}
        </div>
    @else
        <!-- Map Container -->
        <div class="row">
            <div class="col-lg-9 col-md-12">
                <div class="card nestko-feature-card">
                    <div class="card-header d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                        <h5 class="mb-2 mb-md-0">{{ get_label('live_map', 'Live Map') }}</h5>
                        <div class="btn-group btn-group-sm d-none d-md-flex" role="group">
                            <input type="radio" class="btn-check" name="mapType" id="roadmap" value="roadmap" checked>
                            <label class="btn btn-outline-primary nestko-touch-target" for="roadmap">
                                <span class="d-none d-lg-inline">{{ get_label('roadmap', 'Roadmap') }}</span>
                                <i class="bx bx-map d-lg-none"></i>
                            </label>

                            <input type="radio" class="btn-check" name="mapType" id="satellite" value="satellite">
                            <label class="btn btn-outline-primary nestko-touch-target" for="satellite">
                                <span class="d-none d-lg-inline">{{ get_label('satellite', 'Satellite') }}</span>
                                <i class="bx bx-globe d-lg-none"></i>
                            </label>

                            <input type="radio" class="btn-check" name="mapType" id="hybrid" value="hybrid">
                            <label class="btn btn-outline-primary nestko-touch-target" for="hybrid">
                                <span class="d-none d-lg-inline">{{ get_label('hybrid', 'Hybrid') }}</span>
                                <i class="bx bx-layer d-lg-none"></i>
                            </label>
                        </div>
                        <!-- Mobile Map Type Selector -->
                        <div class="dropdown d-md-none">
                            <button class="btn btn-outline-primary btn-sm dropdown-toggle nestko-touch-target" type="button" data-bs-toggle="dropdown">
                                <i class="bx bx-map me-1"></i> <span id="currentMapType">{{ get_label('roadmap', 'Roadmap') }}</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item map-type-option" href="#" data-type="roadmap">{{ get_label('roadmap', 'Roadmap') }}</a></li>
                                <li><a class="dropdown-item map-type-option" href="#" data-type="satellite">{{ get_label('satellite', 'Satellite') }}</a></li>
                                <li><a class="dropdown-item map-type-option" href="#" data-type="hybrid">{{ get_label('hybrid', 'Hybrid') }}</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="nestko-map-container">
                            <div id="nestkoMap" style="height: 100%; width: 100%;"></div>
                            <!-- Mobile Map Controls -->
                            <div class="nestko-map-controls d-md-none">
                                <button type="button" class="btn btn-sm nestko-touch-target" id="zoomInBtn" title="{{ get_label('zoom_in', 'Zoom In') }}">
                                    <i class="bx bx-plus"></i>
                                </button>
                                <button type="button" class="btn btn-sm nestko-touch-target" id="zoomOutBtn" title="{{ get_label('zoom_out', 'Zoom Out') }}">
                                    <i class="bx bx-minus"></i>
                                </button>
                                <button type="button" class="btn btn-sm nestko-touch-target" id="fullscreenBtn" title="{{ get_label('fullscreen', 'Fullscreen') }}">
                                    <i class="bx bx-fullscreen"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-12">
                <!-- Mobile Quick Actions -->
                <div class="d-lg-none mb-3">
                    <div class="nestko-quick-actions">
                        <button type="button" class="nestko-quick-action" id="mobileRefreshBtn">
                            <i class="bx bx-refresh"></i>
                            {{ get_label('refresh', 'Refresh') }}
                        </button>
                        <button type="button" class="nestko-quick-action" id="mobileLocationBtn">
                            <i class="bx bx-current-location"></i>
                            {{ get_label('my_location', 'My Location') }}
                        </button>
                    </div>
                </div>

                <!-- Team Members Panel -->
                <div class="nestko-collapsible mb-3">
                    <div class="nestko-collapsible-header" data-bs-toggle="collapse" data-bs-target="#teamMembersCollapse">
                        <span>
                            <i class="bx bx-group me-2"></i>
                            {{ get_label('team_members', 'Team Members') }}
                        </span>
                        <i class="bx bx-chevron-down"></i>
                    </div>
                    <div class="collapse show" id="teamMembersCollapse">
                        <div class="nestko-collapsible-content">
                            <div id="teamMembersList" class="nestko-team-members-grid">
                                <div class="nestko-loading">
                                    <div class="nestko-loading-spinner"></div>
                                    {{ get_label('loading', 'Loading') }}...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Geofences Panel -->
                <div class="nestko-collapsible">
                    <div class="nestko-collapsible-header" data-bs-toggle="collapse" data-bs-target="#geofencesCollapse">
                        <span>
                            <i class="bx bx-shape-polygon me-2"></i>
                            {{ get_label('geofences', 'Geofences') }}
                        </span>
                        <i class="bx bx-chevron-down"></i>
                    </div>
                    <div class="collapse show" id="geofencesCollapse">
                        <div class="nestko-collapsible-content">
                            <div id="geofencesList" class="nestko-geofences-grid">
                                <div class="nestko-loading">
                                    <div class="nestko-loading-spinner"></div>
                                    {{ get_label('loading', 'Loading') }}...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Floating Action Button -->
        <button type="button" class="nestko-fab d-lg-none" id="mobileFab" title="{{ get_label('quick_actions', 'Quick Actions') }}">
            <i class="bx bx-plus"></i>
        </button>
    @endif
</div>

<!-- Location Permission Modal -->
<div class="modal fade" id="locationPermissionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ get_label('location_permission_required', 'Location Permission Required') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ get_label('location_permission_message', 'This application needs access to your location to provide location-based features. Please allow location access when prompted.') }}</p>
                <div class="alert alert-info">
                    <i class="bx bx-info-circle"></i>
                    {{ get_label('location_privacy_note', 'Your location data is encrypted and only used for work-related purposes within your workspace.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ get_label('cancel', 'Cancel') }}</button>
                <button type="button" class="btn btn-primary" id="enableLocationBtn">{{ get_label('enable_location', 'Enable Location') }}</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@if(isNestkoFeatureEnabled('geolocation'))
<!-- Mapbox GL JS -->
<link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
<script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>

<script>
// NestKo Geolocation Configuration
const NESTKO_CONFIG = {
    map: {
        accessToken: '{{ getNestkoConfig('maps.mapbox.access_token') }}',
        style: '{{ getNestkoConfig('maps.mapbox.style', 'mapbox://styles/mapbox/streets-v11') }}',
        defaultCenter: [
            {{ getNestkoConfig('maps.default_center.lng', -74.0060) }},
            {{ getNestkoConfig('maps.default_center.lat', 40.7128) }}
        ],
        defaultZoom: {{ getNestkoConfig('maps.default_zoom', 10) }},
        trackingInterval: {{ getNestkoConfig('geolocation.tracking.interval', 30000) }}
    },
    routes: {
        updateLocation: '{{ route('nestko.geolocation.location.update') }}',
        getCurrentLocations: '{{ route('nestko.geolocation.locations.current') }}',
        getGeofences: '{{ route('nestko.geolocation.geofences.list') }}',
        findNearbyUsers: '{{ route('nestko.geolocation.users.nearby') }}'
    },
    csrf: '{{ csrf_token() }}'
};

let nestkoMap;
let userMarkers = {};
let geofenceOverlays = {};
let currentLocationMarker;
let locationWatchId;

// Initialize the map
function initNestkoMap() {
    // Set Mapbox access token
    mapboxgl.accessToken = NESTKO_CONFIG.map.accessToken;

    // Initialize map
    nestkoMap = new mapboxgl.Map({
        container: 'nestkoMap',
        style: NESTKO_CONFIG.map.style,
        center: NESTKO_CONFIG.map.defaultCenter,
        zoom: NESTKO_CONFIG.map.defaultZoom
    });

    // Add navigation controls
    nestkoMap.addControl(new mapboxgl.NavigationControl());

    // Add geolocate control
    const geolocate = new mapboxgl.GeolocateControl({
        positionOptions: {
            enableHighAccuracy: true
        },
        trackUserLocation: true,
        showUserHeading: true
    });
    nestkoMap.addControl(geolocate);

    // Wait for map to load
    nestkoMap.on('load', function() {
        // Load initial data
        loadCurrentLocations();
        loadGeofences();

        // Set up auto-refresh
        setInterval(loadCurrentLocations, NESTKO_CONFIG.map.trackingInterval);

        // Set up map type controls
        setupMapTypeControls();

        // Set up location tracking
        setupLocationTracking();
    });
}

// Load current user locations
function loadCurrentLocations() {
    fetch(NESTKO_CONFIG.routes.getCurrentLocations)
        .then(response => response.json())
        .then(locations => {
            updateUserMarkers(locations);
            updateTeamMembersList(locations);
        })
        .catch(error => {
            console.error('Error loading locations:', error);
        });
}

// Load geofences
function loadGeofences() {
    fetch(NESTKO_CONFIG.routes.getGeofences)
        .then(response => response.json())
        .then(geofences => {
            updateGeofenceOverlays(geofences);
            updateGeofencesList(geofences);
        })
        .catch(error => {
            console.error('Error loading geofences:', error);
        });
}

// Update user markers on map
function updateUserMarkers(locations) {
    // Clear existing markers
    Object.values(userMarkers).forEach(marker => marker.remove());
    userMarkers = {};

    locations.forEach(location => {
        // Create custom marker element with responsive sizing
        const el = document.createElement('div');
        el.className = 'nestko-user-marker';
        el.style.backgroundImage = `url(${location.user.photo || '/assets/img/default-avatar.png'})`;

        // Add stale class if needed
        if (location.is_stale) {
            el.classList.add('stale');
        }

        // Create marker
        const marker = new mapboxgl.Marker(el)
            .setLngLat([parseFloat(location.longitude), parseFloat(location.latitude)])
            .addTo(nestkoMap);

        // Create responsive popup
        const popup = new mapboxgl.Popup({
            offset: 25,
            closeButton: true,
            closeOnClick: true,
            className: 'nestko-map-popup'
        }).setHTML(`
            <div class="p-3">
                <div class="d-flex align-items-center mb-2">
                    <img src="${location.user.photo || '/assets/img/default-avatar.png'}"
                         alt="${location.user.name}"
                         class="rounded-circle me-2"
                         style="width: 32px; height: 32px;">
                    <h6 class="mb-0">${location.user.name}</h6>
                </div>
                <p class="mb-1"><small class="text-muted">Last updated: ${new Date(location.recorded_at).toLocaleString()}</small></p>
                ${location.address ? `<p class="mb-2"><small>${location.address}</small></p>` : ''}
                <div class="d-flex justify-content-between align-items-center">
                    <span class="nestko-status-badge ${location.is_stale ? 'stale' : 'live'}">
                        ${location.is_stale ? 'Stale Location' : 'Live'}
                    </span>
                    <button class="btn btn-sm btn-outline-primary" onclick="centerMapOnUser(${location.latitude}, ${location.longitude})">
                        <i class="bx bx-crosshair"></i> Center
                    </button>
                </div>
            </div>
        `);

        // Add click event with touch support
        el.addEventListener('click', (e) => {
            e.stopPropagation();
            popup.addTo(nestkoMap);
            marker.setPopup(popup);
        });

        // Add touch events for mobile
        let touchStartTime;
        el.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        });

        el.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            if (touchDuration < 500) { // Short tap
                e.preventDefault();
                popup.addTo(nestkoMap);
                marker.setPopup(popup);
            }
        });

        userMarkers[location.user.id] = marker;
    });
}

// Update geofence overlays
function updateGeofenceOverlays(geofences) {
    // Clear existing overlays
    Object.values(geofenceOverlays).forEach(overlay => {
        if (nestkoMap.getLayer(overlay.layerId)) {
            nestkoMap.removeLayer(overlay.layerId);
        }
        if (nestkoMap.getSource(overlay.sourceId)) {
            nestkoMap.removeSource(overlay.sourceId);
        }
    });
    geofenceOverlays = {};

    geofences.forEach(geofence => {
        const sourceId = `geofence-source-${geofence.id}`;
        const layerId = `geofence-layer-${geofence.id}`;

        let geoJsonData;

        if (geofence.type === 'circle') {
            // Create circle using turf.js or manual calculation
            const center = [parseFloat(geofence.center_longitude), parseFloat(geofence.center_latitude)];
            const radiusInKm = geofence.radius_meters / 1000;

            // Simple circle approximation (you might want to use turf.js for better circles)
            const points = [];
            const steps = 64;
            for (let i = 0; i < steps; i++) {
                const angle = (i * 360) / steps;
                const dx = radiusInKm * Math.cos(angle * Math.PI / 180) / 111.32; // rough conversion
                const dy = radiusInKm * Math.sin(angle * Math.PI / 180) / 110.54; // rough conversion
                points.push([center[0] + dx, center[1] + dy]);
            }
            points.push(points[0]); // Close the polygon

            geoJsonData = {
                type: 'Feature',
                geometry: {
                    type: 'Polygon',
                    coordinates: [points]
                }
            };
        } else if (geofence.type === 'polygon' && geofence.polygon_coordinates) {
            const coordinates = geofence.polygon_coordinates.map(coord => [
                parseFloat(coord.lng),
                parseFloat(coord.lat)
            ]);
            coordinates.push(coordinates[0]); // Close the polygon

            geoJsonData = {
                type: 'Feature',
                geometry: {
                    type: 'Polygon',
                    coordinates: [coordinates]
                }
            };
        }

        if (geoJsonData) {
            // Add source
            nestkoMap.addSource(sourceId, {
                type: 'geojson',
                data: geoJsonData
            });

            // Add fill layer
            nestkoMap.addLayer({
                id: layerId,
                type: 'fill',
                source: sourceId,
                paint: {
                    'fill-color': '#FF0000',
                    'fill-opacity': 0.15
                }
            });

            // Add stroke layer
            nestkoMap.addLayer({
                id: `${layerId}-stroke`,
                type: 'line',
                source: sourceId,
                paint: {
                    'line-color': '#FF0000',
                    'line-width': 2,
                    'line-opacity': 0.8
                }
            });

            // Add click event for popup
            nestkoMap.on('click', layerId, (e) => {
                const popup = new mapboxgl.Popup()
                    .setLngLat(e.lngLat)
                    .setHTML(`
                        <div class="p-2">
                            <h6>${geofence.name}</h6>
                            ${geofence.description ? `<p>${geofence.description}</p>` : ''}
                            <div>
                                ${geofence.alert_on_enter ? '<span class="badge bg-info me-1">Enter Alert</span>' : ''}
                                ${geofence.alert_on_exit ? '<span class="badge bg-warning">Exit Alert</span>' : ''}
                            </div>
                        </div>
                    `)
                    .addTo(nestkoMap);
            });

            // Change cursor on hover
            nestkoMap.on('mouseenter', layerId, () => {
                nestkoMap.getCanvas().style.cursor = 'pointer';
            });

            nestkoMap.on('mouseleave', layerId, () => {
                nestkoMap.getCanvas().style.cursor = '';
            });

            geofenceOverlays[geofence.id] = { sourceId, layerId };
        }
    });
}

// Update team members list with responsive design
function updateTeamMembersList(locations) {
    const container = document.getElementById('teamMembersList');

    if (locations.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">{{ get_label('no_active_locations', 'No active locations') }}</p>';
        return;
    }

    const html = locations.map(location => `
        <div class="nestko-team-member" onclick="centerMapOnUser(${location.latitude}, ${location.longitude})" style="cursor: pointer;">
            <img src="${location.user.photo || '/assets/img/default-avatar.png'}"
                 alt="${location.user.name}"
                 class="nestko-team-member-avatar">
            <div class="nestko-team-member-info">
                <div class="nestko-team-member-name">${location.user.name}</div>
                <div class="nestko-team-member-status">
                    <small class="text-muted">${new Date(location.recorded_at).toLocaleTimeString()}</small>
                    ${location.address ? `<br><small class="text-muted">${location.address.substring(0, 30)}...</small>` : ''}
                </div>
            </div>
            <div class="d-flex flex-column align-items-end">
                <span class="nestko-status-badge ${location.is_stale ? 'stale' : 'live'} mb-1">
                    ${location.is_stale ? 'Stale' : 'Live'}
                </span>
                <button class="btn btn-sm btn-outline-primary nestko-touch-target" onclick="event.stopPropagation(); findNearbyUsers(${location.latitude}, ${location.longitude})">
                    <i class="bx bx-radar"></i>
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Update geofences list with responsive design
function updateGeofencesList(geofences) {
    const container = document.getElementById('geofencesList');

    if (geofences.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">{{ get_label('no_geofences', 'No geofences configured') }}</p>';
        return;
    }

    const html = geofences.map(geofence => `
        <div class="nestko-geofence-item ${geofence.alert_on_enter ? 'alert-enter' : ''} ${geofence.alert_on_exit ? 'alert-exit' : ''}"
             onclick="centerMapOnGeofence('${geofence.id}')" style="cursor: pointer;">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="nestko-geofence-name">${geofence.name}</div>
                <span class="badge bg-secondary">${geofence.type}</span>
            </div>
            <div class="nestko-geofence-description">${geofence.description || 'No description'}</div>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    ${geofence.alert_on_enter ? '<span class="nestko-status-badge live me-1">Enter Alert</span>' : ''}
                    ${geofence.alert_on_exit ? '<span class="nestko-status-badge stale">Exit Alert</span>' : ''}
                </div>
                <button class="btn btn-sm btn-outline-primary nestko-touch-target" onclick="event.stopPropagation(); showGeofenceDetails('${geofence.id}')">
                    <i class="bx bx-info-circle"></i>
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Setup map type controls
function setupMapTypeControls() {
    document.querySelectorAll('input[name="mapType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                let style;
                switch(this.value) {
                    case 'roadmap':
                        style = 'mapbox://styles/mapbox/streets-v11';
                        break;
                    case 'satellite':
                        style = 'mapbox://styles/mapbox/satellite-v9';
                        break;
                    case 'hybrid':
                        style = 'mapbox://styles/mapbox/satellite-streets-v11';
                        break;
                    default:
                        style = 'mapbox://styles/mapbox/streets-v11';
                }
                nestkoMap.setStyle(style);
            }
        });
    });
}

// Setup location tracking
function setupLocationTracking() {
    document.getElementById('myLocationBtn').addEventListener('click', function() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                position => {
                    const pos = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    nestkoMap.setCenter([pos.lng, pos.lat]);
                    nestkoMap.setZoom(15);

                    // Update current location marker
                    if (currentLocationMarker) {
                        currentLocationMarker.remove();
                    }

                    // Create custom location marker element
                    const el = document.createElement('div');
                    el.className = 'current-location-marker';
                    el.style.width = '24px';
                    el.style.height = '24px';
                    el.style.borderRadius = '50%';
                    el.style.backgroundColor = '#4285F4';
                    el.style.border = '3px solid white';
                    el.style.boxShadow = '0 2px 6px rgba(0,0,0,0.3)';

                    currentLocationMarker = new mapboxgl.Marker(el)
                        .setLngLat([pos.lng, pos.lat])
                        .addTo(nestkoMap);
                    
                    // Send location update
                    updateLocationOnServer(position);
                },
                error => {
                    console.error('Geolocation error:', error);
                    if (error.code === error.PERMISSION_DENIED) {
                        $('#locationPermissionModal').modal('show');
                    }
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        } else {
            toastr.error('{{ get_label('geolocation_not_supported', 'Geolocation is not supported by this browser') }}');
        }
    });
    
    // Auto-track location if permission granted
    if (navigator.geolocation) {
        locationWatchId = navigator.geolocation.watchPosition(
            position => updateLocationOnServer(position),
            error => console.warn('Location tracking error:', error),
            {
                enableHighAccuracy: false,
                timeout: 30000,
                maximumAge: NESTKO_CONFIG.map.trackingInterval
            }
        );
    }
}

// Update location on server
function updateLocationOnServer(position) {
    const data = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        altitude: position.coords.altitude,
        heading: position.coords.heading,
        speed: position.coords.speed,
        _token: NESTKO_CONFIG.csrf
    };
    
    fetch(NESTKO_CONFIG.routes.updateLocation, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': NESTKO_CONFIG.csrf
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (!result.success) {
            console.error('Failed to update location:', result.message);
        }
    })
    .catch(error => {
        console.error('Error updating location:', error);
    });
}

// Responsive helper functions
function centerMapOnUser(lat, lng) {
    if (nestkoMap) {
        nestkoMap.flyTo({
            center: [lng, lat],
            zoom: 15,
            duration: 1000
        });
    }
}

function centerMapOnGeofence(geofenceId) {
    // Find geofence and center map on it
    // This would need to be implemented based on stored geofence data
    console.log('Centering on geofence:', geofenceId);
}

function showGeofenceDetails(geofenceId) {
    // Show geofence details modal
    console.log('Showing geofence details:', geofenceId);
    toastr.info('Geofence details feature coming soon!');
}

function findNearbyUsers(lat, lng) {
    // Find users near this location
    const data = {
        latitude: lat,
        longitude: lng,
        radius_meters: 1000,
        _token: NESTKO_CONFIG.csrf
    };

    fetch(NESTKO_CONFIG.routes.findNearbyUsers, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': NESTKO_CONFIG.csrf
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(users => {
        if (users.length > 0) {
            toastr.success(`Found ${users.length} nearby team member(s)`);
        } else {
            toastr.info('No team members found nearby');
        }
    })
    .catch(error => {
        console.error('Error finding nearby users:', error);
        toastr.error('Failed to find nearby users');
    });
}

function toggleFullscreen() {
    const mapContainer = document.getElementById('nestkoMap');
    if (!document.fullscreenElement) {
        mapContainer.requestFullscreen().catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map when DOM is ready
    initNestkoMap();

    // Desktop refresh button
    const refreshBtn = document.getElementById('refreshLocations');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadCurrentLocations();
            loadGeofences();
            toastr.success('{{ get_label('locations_refreshed', 'Locations refreshed') }}');
        });
    }

    // Mobile refresh button
    const mobileRefreshBtn = document.getElementById('mobileRefreshBtn');
    if (mobileRefreshBtn) {
        mobileRefreshBtn.addEventListener('click', function() {
            loadCurrentLocations();
            loadGeofences();
            toastr.success('{{ get_label('locations_refreshed', 'Locations refreshed') }}');
        });
    }

    // Mobile location button
    const mobileLocationBtn = document.getElementById('mobileLocationBtn');
    if (mobileLocationBtn) {
        mobileLocationBtn.addEventListener('click', function() {
            document.getElementById('myLocationBtn').click();
        });
    }

    // Mobile map controls
    const zoomInBtn = document.getElementById('zoomInBtn');
    if (zoomInBtn) {
        zoomInBtn.addEventListener('click', function() {
            nestkoMap.zoomIn();
        });
    }

    const zoomOutBtn = document.getElementById('zoomOutBtn');
    if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', function() {
            nestkoMap.zoomOut();
        });
    }

    const fullscreenBtn = document.getElementById('fullscreenBtn');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }

    // Mobile floating action button
    const mobileFab = document.getElementById('mobileFab');
    if (mobileFab) {
        mobileFab.addEventListener('click', function() {
            // Toggle between refresh and location actions
            if (this.dataset.action === 'location') {
                document.getElementById('myLocationBtn').click();
                this.innerHTML = '<i class="bx bx-refresh"></i>';
                this.dataset.action = 'refresh';
                this.title = '{{ get_label('refresh', 'Refresh') }}';
            } else {
                loadCurrentLocations();
                loadGeofences();
                this.innerHTML = '<i class="bx bx-current-location"></i>';
                this.dataset.action = 'location';
                this.title = '{{ get_label('my_location', 'My Location') }}';
                toastr.success('{{ get_label('locations_refreshed', 'Locations refreshed') }}');
            }
        });
    }

    // Mobile map type selector
    document.querySelectorAll('.map-type-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const type = this.dataset.type;
            const currentMapTypeSpan = document.getElementById('currentMapType');

            // Update button text
            currentMapTypeSpan.textContent = this.textContent;

            // Change map style
            let style;
            switch(type) {
                case 'roadmap':
                    style = 'mapbox://styles/mapbox/streets-v11';
                    break;
                case 'satellite':
                    style = 'mapbox://styles/mapbox/satellite-v9';
                    break;
                case 'hybrid':
                    style = 'mapbox://styles/mapbox/satellite-streets-v11';
                    break;
                default:
                    style = 'mapbox://styles/mapbox/streets-v11';
            }
            nestkoMap.setStyle(style);
        });
    });

    // Enable location button in modal
    const enableLocationBtn = document.getElementById('enableLocationBtn');
    if (enableLocationBtn) {
        enableLocationBtn.addEventListener('click', function() {
            $('#locationPermissionModal').modal('hide');
            document.getElementById('myLocationBtn').click();
        });
    }

    // Handle orientation change for mobile
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            if (nestkoMap) {
                nestkoMap.resize();
            }
        }, 100);
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (nestkoMap) {
            nestkoMap.resize();
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (locationWatchId) {
        navigator.geolocation.clearWatch(locationWatchId);
    }
});
</script>
@endif
@endsection
