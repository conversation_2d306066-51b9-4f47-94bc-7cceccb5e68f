<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiConversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'workspace_id',
        'conversation_id',
        'role',
        'encrypted_content',
        'metadata',
        'project_id',
        'task_id',
        'context_type',
        'ai_model',
        'tokens_used',
        'cost',
        'response_time_ms',
        'user_feedback',
        'user_feedback_note',
        'ip_address',
        'user_agent',
        'is_flagged',
    ];

    protected $casts = [
        'metadata' => 'array',
        'cost' => 'decimal:4',
        'is_flagged' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the conversation
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the workspace this conversation belongs to
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Get the associated project
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the associated task
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Scope to get conversations by context type
     */
    public function scopeByContext($query, string $contextType)
    {
        return $query->where('context_type', $contextType);
    }

    /**
     * Scope to get conversations by role
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope to get recent conversations
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get decrypted content
     */
    public function getDecryptedContentAttribute(): string
    {
        try {
            return decrypt($this->encrypted_content);
        } catch (\Exception $e) {
            return '[Content could not be decrypted]';
        }
    }

    /**
     * Get formatted cost
     */
    public function getFormattedCostAttribute(): string
    {
        return '$' . number_format($this->cost, 4);
    }

    /**
     * Check if conversation is from user
     */
    public function isFromUser(): bool
    {
        return $this->role === 'user';
    }

    /**
     * Check if conversation is from assistant
     */
    public function isFromAssistant(): bool
    {
        return $this->role === 'assistant';
    }

    /**
     * Get conversation statistics for a workspace
     */
    public static function getWorkspaceStats(int $workspaceId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $totalConversations = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $totalTokens = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->sum('tokens_used');

        $totalCost = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->sum('cost');

        $avgResponseTime = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->where('role', 'assistant')
            ->avg('response_time_ms');

        return [
            'total_conversations' => $totalConversations,
            'total_tokens' => $totalTokens,
            'total_cost' => $totalCost,
            'avg_response_time_ms' => round($avgResponseTime ?? 0),
            'period_days' => $days,
        ];
    }
}
