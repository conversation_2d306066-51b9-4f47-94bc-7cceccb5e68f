/*
 * Mobile Responsive Styles for Front-End
 * Zoho-themed red color scheme with mobile optimizations
 */

/* ==========================================================================
   Zoho Red Theme Colors
   ========================================================================== */
:root {
    --zoho-red: #d32f2f;
    --zoho-red-dark: #b71c1c;
    --zoho-red-light: #f44336;
    --zoho-red-hover: #c62828;
    --zoho-red-gradient: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
}

/* ==========================================================================
   Mobile Sticky Header
   ========================================================================== */
@media (max-width: 768px) {
    .navbar.fixed-top {
        position: fixed !important;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1050;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        min-height: 70px;
        display: flex;
        align-items: center;
    }

    .navbar.scrolled {
        background: rgba(255, 255, 255, 0.98) !important;
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
    }

    /* Ensure navbar stays visible and accessible */
    .navbar.fixed-top {
        transform: translateY(0) !important;
        opacity: 1 !important;
    }

    /* Ensure content doesn't hide behind fixed navbar */
    body {
        padding-top: 80px;
    }

    .custom-hero-header {
        margin-top: -80px;
        padding-top: 80px;
    }

    /* Improve navbar brand visibility */
    .navbar-brand img {
        max-height: 50px;
        width: auto;
    }

    /* Better navbar toggler */
    .navbar-toggler {
        padding: 8px 12px;
        border: 2px solid var(--zoho-red);
        border-radius: 8px;
    }
}

/* ==========================================================================
   To-Top Button Mobile Styling
   ========================================================================== */
@media (max-width: 768px) {
    .to-top,
    .back-to-top,
    .scroll-to-top,
    [class*="to-top"],
    [class*="back-to-top"],
    [class*="scroll-to-top"],
    #backToTopBtn {
        display: block !important;
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        width: 50px !important;
        height: 50px !important;
        border-radius: 50% !important;
        background: var(--zoho-red-gradient) !important;
        border: none !important;
        color: white !important;
        font-size: 18px !important;
        box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3) !important;
        z-index: 1000 !important;
        transition: all 0.3s ease !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(20px) !important;
    }

    .to-top.show,
    .back-to-top.show,
    .scroll-to-top.show,
    [class*="to-top"].show,
    [class*="back-to-top"].show,
    [class*="scroll-to-top"].show,
    #backToTopBtn.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    .to-top:hover,
    .back-to-top:hover,
    .scroll-to-top:hover,
    [class*="to-top"]:hover,
    [class*="back-to-top"]:hover,
    [class*="scroll-to-top"]:hover,
    #backToTopBtn:hover {
        background: var(--zoho-red-hover) !important;
        transform: translateY(-3px) !important;
        box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4) !important;
    }

    /* Hide text label on mobile, show only icon */
    .to-top .vertical-label,
    .back-to-top .vertical-label,
    .scroll-to-top .vertical-label,
    #backToTopBtn .vertical-label {
        display: none !important;
    }
}

/* ==========================================================================
   Mobile Hero Image Responsive
   ========================================================================== */
@media (max-width: 768px) {
    .hero-images-section {
        padding: 2rem 0 !important;
    }

    .main-image-container {
        width: 100%;
        max-width: 450px;
        margin: 0 auto;
        position: relative;
    }

    .main-image-container img {
        width: 100%;
        height: auto;
        min-height: 300px;
        max-height: 400px;
        object-fit: cover;
        border-radius: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    .main-image-container:hover img {
        transform: scale(1.02);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }

    /* Hide floating images on mobile */
    .floating-card.top-right,
    .floating-card.bottom-left {
        display: none !important;
    }

    /* Adjust hero section spacing */
    .page-header {
        padding: 3rem 0 2rem 0 !important;
    }

    .page-header h1 {
        font-size: 2.2rem !important;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }

    .page-header p {
        font-size: 1.1rem !important;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    /* Hero section container */
    .custom-hero-header {
        padding: 2rem 0 !important;
    }

    /* Position relative container for hero images */
    .position-relative {
        margin: 2rem 0;
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .main-image-container {
        max-width: 500px;
    }

    .main-image-container img {
        min-height: 350px;
        max-height: 450px;
    }

    /* Show floating cards but make them smaller */
    .floating-card.top-right,
    .floating-card.bottom-left {
        transform: scale(0.8);
    }
}

/* ==========================================================================
   Zoho Red Button Theme
   ========================================================================== */
/* Primary buttons - Zoho red theme */
.btn-primary,
.btn-gradient-dark,
.btn-outline-primary {
    background: var(--zoho-red-gradient) !important;
    border-color: var(--zoho-red) !important;
    color: white !important;
    transition: all 0.3s ease;
}

.btn-primary:hover,
.btn-gradient-dark:hover,
.btn-outline-primary:hover {
    background: var(--zoho-red-hover) !important;
    border-color: var(--zoho-red-dark) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.btn-outline-primary {
    background: transparent !important;
    color: var(--zoho-red) !important;
    border: 2px solid var(--zoho-red) !important;
}

.btn-outline-primary:hover {
    background: var(--zoho-red) !important;
    color: white !important;
}

/* Login/Register buttons */
.btn-login,
.btn-register,
.btn-get-started {
    background: var(--zoho-red-gradient) !important;
    border-color: var(--zoho-red) !important;
    color: white !important;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-login:hover,
.btn-register:hover,
.btn-get-started:hover {
    background: var(--zoho-red-hover) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

/* Pricing plan buttons - consistent styling across devices */
.pricing-plan .btn,
.plan-card .btn,
.subscription-btn,
.btn-outline-primary,
.bg-gradient-dark,
.btn-check + .btn-outline-primary {
    background: var(--zoho-red-gradient) !important;
    border-color: var(--zoho-red) !important;
    color: white !important;
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 12px 24px !important;
    text-transform: none !important;
    letter-spacing: normal !important;
}

.pricing-plan .btn:hover,
.plan-card .btn:hover,
.subscription-btn:hover,
.btn-outline-primary:hover,
.bg-gradient-dark:hover,
.btn-check + .btn-outline-primary:hover {
    background: var(--zoho-red-hover) !important;
    border-color: var(--zoho-red-dark) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4) !important;
}

/* Active/checked state for pricing toggle buttons */
.btn-check:checked + .btn-outline-primary {
    background: var(--zoho-red-dark) !important;
    border-color: var(--zoho-red-dark) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(211, 47, 47, 0.3) !important;
}

/* Ensure consistent button group styling */
.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px !important;
}

.btn-group .btn:first-child {
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
}

.btn-group .btn:last-child {
    border-top-left-radius: 8px !important;
    border-bottom-left-radius: 8px !important;
}

/* ==========================================================================
   Mobile Navigation Improvements
   ========================================================================== */
@media (max-width: 768px) {
    .navbar-toggler {
        border: none !important;
        padding: 8px;
        background: var(--zoho-red) !important;
        border-radius: 8px;
    }
    
    .navbar-toggler-bar {
        background: white !important;
        height: 2px;
        width: 20px;
        display: block;
        margin: 3px 0;
        transition: all 0.3s ease;
    }
    
    .navbar-collapse {
        background: white;
        margin-top: 10px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        padding: 1rem !important;
    }
    
    .nav-link {
        padding: 12px 16px !important;
        margin: 4px 0 !important;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .nav-link:hover,
    .nav-link.active {
        background: rgba(211, 47, 47, 0.1) !important;
        color: var(--zoho-red) !important;
    }
}

/* ==========================================================================
   Default Avatar SVG Styles
   ========================================================================== */
.default-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--zoho-red-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.default-avatar svg {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

/* ==========================================================================
   Chat Widget Styles
   ========================================================================== */
.chat-widget-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

.chat-widget-btn {
    background: var(--zoho-red-gradient) !important;
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 16px rgba(211, 47, 47, 0.3);
    transition: all 0.3s ease;
}

.chat-widget-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

/* ==========================================================================
   Timer Icon Styles
   ========================================================================== */
.timer-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

.timer-widget {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--zoho-red);
}

/* ==========================================================================
   Mobile Form Improvements
   ========================================================================== */
@media (max-width: 768px) {
    .form-control {
        padding: 12px 16px;
        font-size: 16px; /* Prevent zoom on iOS */
        border-radius: 8px;
        border: 2px solid #e0e0e0;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: var(--zoho-red);
        box-shadow: 0 0 0 0.2rem rgba(211, 47, 47, 0.25);
    }
    
    .input-group {
        margin-bottom: 1rem;
    }
    
    label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }
}

/* ==========================================================================
   Mobile Card Improvements
   ========================================================================== */
@media (max-width: 768px) {
    .card {
        border-radius: 16px;
        border: none;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        border-radius: 16px 16px 0 0 !important;
        padding: 1.25rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

/* ==========================================================================
   Mobile Typography
   ========================================================================== */
@media (max-width: 768px) {
    h1 { font-size: 2rem !important; }
    h2 { font-size: 1.75rem !important; }
    h3 { font-size: 1.5rem !important; }
    h4 { font-size: 1.25rem !important; }
    h5 { font-size: 1.1rem !important; }
    h6 { font-size: 1rem !important; }
    
    p, .text-body {
        font-size: 1rem;
        line-height: 1.6;
    }
    
    .lead {
        font-size: 1.1rem;
    }
    
    small, .small {
        font-size: 0.875rem;
    }
}

/* ==========================================================================
   Mobile Spacing Utilities
   ========================================================================== */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .section-padding {
        padding: 3rem 0;
    }
    
    .mb-mobile-4 { margin-bottom: 2rem !important; }
    .mt-mobile-4 { margin-top: 2rem !important; }
    .py-mobile-5 { padding: 3rem 0 !important; }
    .px-mobile-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
}

/* ==========================================================================
   Mobile Animation Improvements
   ========================================================================== */
@media (max-width: 768px) {
    .fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }
    
    .slide-up {
        animation: slideUp 0.6s ease-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes slideUp {
        from { 
            opacity: 0;
            transform: translateY(30px);
        }
        to { 
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* ==========================================================================
   Mobile Touch Improvements
   ========================================================================== */
@media (max-width: 768px) {
    .btn, .nav-link, .card, .form-control {
        -webkit-tap-highlight-color: rgba(211, 47, 47, 0.2);
    }

    .touch-target {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Improve scroll performance */
    * {
        -webkit-overflow-scrolling: touch;
    }

    /* Ensure pricing buttons are properly sized on mobile */
    .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
    }

    .btn-group .btn {
        flex: 1;
        min-width: 100px;
        margin: 0 !important;
    }

    /* Fix button group radio styling on mobile */
    .btn-check + .btn {
        border-radius: 8px !important;
    }

    .btn-check:checked + .btn {
        background: var(--zoho-red-dark) !important;
        border-color: var(--zoho-red-dark) !important;
        color: white !important;
    }
}

/* ==========================================================================
   Tablet and Desktop Consistency
   ========================================================================== */
@media (min-width: 769px) {
    /* Ensure consistent button styling on larger screens */
    .btn-outline-primary,
    .bg-gradient-dark,
    .btn-check + .btn-outline-primary {
        background: var(--zoho-red-gradient) !important;
        border-color: var(--zoho-red) !important;
        color: white !important;
        transition: all 0.3s ease !important;
    }

    .btn-outline-primary:hover,
    .bg-gradient-dark:hover,
    .btn-check + .btn-outline-primary:hover {
        background: var(--zoho-red-hover) !important;
        border-color: var(--zoho-red-dark) !important;
        color: white !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4) !important;
    }

    .btn-check:checked + .btn-outline-primary {
        background: var(--zoho-red-dark) !important;
        border-color: var(--zoho-red-dark) !important;
        color: white !important;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */
@media (prefers-color-scheme: dark) {
    .navbar.fixed-top {
        background: rgba(33, 37, 41, 0.95) !important;
    }
    
    .card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .form-control {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
}
