<?php

namespace App\Services\NestKo;

use App\Models\User;
use App\Models\UserLocation;
use App\Models\Geofence;
use App\Models\LocationHistory;
use App\Services\NestKo\GeofenceAlertService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class LocationTrackingService
{
    protected $geofenceAlertService;

    public function __construct(GeofenceAlertService $geofenceAlertService)
    {
        $this->geofenceAlertService = $geofenceAlertService;
    }

    /**
     * Update user's location
     */
    public function updateUserLocation(
        int $userId,
        int $workspaceId,
        float $latitude,
        float $longitude,
        array $options = []
    ): UserLocation {
        if (!canAccessGeolocation()) {
            throw new \Exception('Geolocation feature is not enabled');
        }

        DB::beginTransaction();
        try {
            // Deactivate previous active location
            UserLocation::where('user_id', $userId)
                ->where('workspace_id', $workspaceId)
                ->where('is_active', true)
                ->update(['is_active' => false]);

            // Create new location record
            $location = UserLocation::create([
                'user_id' => $userId,
                'workspace_id' => $workspaceId,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'accuracy' => $options['accuracy'] ?? null,
                'altitude' => $options['altitude'] ?? null,
                'heading' => $options['heading'] ?? null,
                'speed' => $options['speed'] ?? null,
                'address' => $options['address'] ?? null,
                'metadata' => $options['metadata'] ?? null,
                'is_active' => true,
                'recorded_at' => $options['recorded_at'] ?? now(),
            ]);

            // Log location update to history
            $this->logLocationEvent($userId, $workspaceId, $latitude, $longitude, 'manual_update', $options);

            // Check geofences
            $this->checkGeofences($userId, $workspaceId, $latitude, $longitude);

            DB::commit();
            return $location;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update user location', [
                'user_id' => $userId,
                'workspace_id' => $workspaceId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get current location for user
     */
    public function getCurrentLocation(int $userId, int $workspaceId): ?UserLocation
    {
        return UserLocation::where('user_id', $userId)
            ->where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get location history for user
     */
    public function getLocationHistory(
        int $userId,
        int $workspaceId,
        Carbon $startDate = null,
        Carbon $endDate = null,
        int $limit = 100
    ) {
        $query = UserLocation::where('user_id', $userId)
            ->where('workspace_id', $workspaceId)
            ->orderBy('recorded_at', 'desc');

        if ($startDate) {
            $query->where('recorded_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('recorded_at', '<=', $endDate);
        }

        return $query->limit($limit)->get();
    }

    /**
     * Get users within radius of a location
     */
    public function getUsersNearLocation(
        int $workspaceId,
        float $latitude,
        float $longitude,
        int $radiusMeters,
        array $excludeUserIds = []
    ) {
        $query = UserLocation::with('user')
            ->where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->withinRadius($latitude, $longitude, $radiusMeters);

        if (!empty($excludeUserIds)) {
            $query->whereNotIn('user_id', $excludeUserIds);
        }

        return $query->get();
    }

    /**
     * Check if user is within any geofences
     */
    protected function checkGeofences(int $userId, int $workspaceId, float $latitude, float $longitude): void
    {
        $geofences = Geofence::where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->get();

        foreach ($geofences as $geofence) {
            $isInside = $geofence->containsPoint($latitude, $longitude);
            
            // Check if user was previously inside this geofence
            $wasInside = $this->wasUserInGeofence($userId, $geofence->id);

            if ($isInside && !$wasInside && $geofence->alert_on_enter) {
                // User entered geofence
                $this->geofenceAlertService->sendGeofenceAlert($geofence, $userId, 'enter');
                $this->logLocationEvent($userId, $workspaceId, $latitude, $longitude, 'geofence_enter', [
                    'geofence_id' => $geofence->id
                ]);
            } elseif (!$isInside && $wasInside && $geofence->alert_on_exit) {
                // User exited geofence
                $this->geofenceAlertService->sendGeofenceAlert($geofence, $userId, 'exit');
                $this->logLocationEvent($userId, $workspaceId, $latitude, $longitude, 'geofence_exit', [
                    'geofence_id' => $geofence->id
                ]);
            }
        }
    }

    /**
     * Check if user was previously in geofence
     */
    protected function wasUserInGeofence(int $userId, int $geofenceId): bool
    {
        $lastEvent = LocationHistory::where('user_id', $userId)
            ->where('geofence_id', $geofenceId)
            ->whereIn('event_type', ['geofence_enter', 'geofence_exit'])
            ->orderBy('recorded_at', 'desc')
            ->first();

        return $lastEvent && $lastEvent->event_type === 'geofence_enter';
    }

    /**
     * Log location event to history
     */
    protected function logLocationEvent(
        int $userId,
        int $workspaceId,
        float $latitude,
        float $longitude,
        string $eventType,
        array $options = []
    ): void {
        // Encrypt coordinates for privacy
        $encryptedLat = encrypt($latitude);
        $encryptedLng = encrypt($longitude);
        
        // Create hash for tamper detection
        $hashData = [
            'user_id' => $userId,
            'workspace_id' => $workspaceId,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'event_type' => $eventType,
            'recorded_at' => $options['recorded_at'] ?? now(),
        ];
        $hash = hash('sha256', json_encode($hashData));

        LocationHistory::create([
            'user_id' => $userId,
            'workspace_id' => $workspaceId,
            'encrypted_latitude' => $encryptedLat,
            'encrypted_longitude' => $encryptedLng,
            'accuracy' => $options['accuracy'] ?? null,
            'address_hash' => $options['address'] ? hash('sha256', $options['address']) : null,
            'event_type' => $eventType,
            'task_id' => $options['task_id'] ?? null,
            'project_id' => $options['project_id'] ?? null,
            'geofence_id' => $options['geofence_id'] ?? null,
            'device_id' => $options['device_id'] ?? null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => $options['metadata'] ?? null,
            'hash' => $hash,
            'recorded_at' => $options['recorded_at'] ?? now(),
        ]);
    }

    /**
     * Clean up old location data
     */
    public function cleanupOldLocations(): void
    {
        $retentionDays = getNestkoConfig('security.data_retention.location_history', 90);
        $cutoffDate = now()->subDays($retentionDays);

        // Delete old user locations (keep only recent active ones)
        UserLocation::where('recorded_at', '<', $cutoffDate)
            ->where('is_active', false)
            ->delete();

        // Archive old location history (don't delete for audit purposes)
        // This could be moved to cold storage instead
        Log::info('Location cleanup completed', [
            'cutoff_date' => $cutoffDate,
            'retention_days' => $retentionDays
        ]);
    }
}
