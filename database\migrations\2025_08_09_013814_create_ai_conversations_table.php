<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_conversations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('workspace_id'); // No foreign key constraint due to pivot relationship
            $table->string('conversation_id')->unique(); // UUID for conversation

            // Message content (encrypted for privacy)
            $table->enum('role', ['user', 'assistant', 'system']);
            $table->text('encrypted_content'); // Encrypted message content
            $table->json('metadata')->nullable(); // Message metadata

            // Context information
            $table->foreignId('project_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('task_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('context_type', ['general', 'project', 'task', 'summary', 'suggestion']);

            // AI model information
            $table->string('ai_model')->default('glm4.5-air');
            $table->integer('tokens_used')->nullable();
            $table->decimal('cost', 8, 4)->nullable(); // Cost in USD

            // Response quality tracking
            $table->integer('response_time_ms')->nullable();
            $table->enum('user_feedback', ['helpful', 'not_helpful', 'irrelevant'])->nullable();
            $table->text('user_feedback_note')->nullable();

            // Audit and security
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->boolean('is_flagged')->default(false); // For content moderation
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'conversation_id']);
            $table->index(['workspace_id', 'created_at']);
            $table->index(['project_id', 'task_id']);
            $table->index(['context_type', 'created_at']);
            $table->index('role');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_conversations');
    }
};
