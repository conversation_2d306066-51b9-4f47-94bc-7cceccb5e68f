/**
 * NestKo Mobile Enhancement JavaScript
 * Provides touch interactions, gestures, and mobile-specific features
 */

class NestKoMobile {
    constructor() {
        this.isTouch = 'ontouchstart' in window;
        this.isMobile = window.innerWidth <= 768;
        this.isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        
        this.init();
    }

    init() {
        if (this.isTouch) {
            this.setupTouchEvents();
            this.setupSwipeGestures();
            this.setupPullToRefresh();
        }
        
        this.setupResponsiveFeatures();
        this.setupKeyboardHandling();
        this.setupOrientationHandling();
    }

    /**
     * Setup touch-specific event handlers
     */
    setupTouchEvents() {
        // Improve touch responsiveness
        document.addEventListener('touchstart', function() {}, { passive: true });
        
        // Handle touch feedback for buttons
        document.querySelectorAll('.nestko-touch-target, .nestko-quick-action, .nestko-fab').forEach(element => {
            element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
            element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
            element.addEventListener('touchcancel', this.handleTouchEnd.bind(this), { passive: true });
        });

        // Prevent double-tap zoom on specific elements
        document.querySelectorAll('.nestko-message-bubble, .nestko-chat-input').forEach(element => {
            element.addEventListener('touchend', function(e) {
                e.preventDefault();
            });
        });
    }

    /**
     * Handle touch start for visual feedback
     */
    handleTouchStart(e) {
        const element = e.currentTarget;
        element.style.transform = 'scale(0.95)';
        element.style.opacity = '0.8';
        element.style.transition = 'all 0.1s ease';
    }

    /**
     * Handle touch end to restore element
     */
    handleTouchEnd(e) {
        const element = e.currentTarget;
        setTimeout(() => {
            element.style.transform = '';
            element.style.opacity = '';
        }, 100);
    }

    /**
     * Setup swipe gesture recognition
     */
    setupSwipeGestures() {
        let startX, startY, startTime;
        const minSwipeDistance = 50;
        const maxSwipeTime = 300;

        document.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            startTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const touch = e.changedTouches[0];
            const endX = touch.clientX;
            const endY = touch.clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            if (deltaTime > maxSwipeTime) return;

            const absX = Math.abs(deltaX);
            const absY = Math.abs(deltaY);

            if (absX > minSwipeDistance && absX > absY) {
                // Horizontal swipe
                if (deltaX > 0) {
                    this.handleSwipeRight(e);
                } else {
                    this.handleSwipeLeft(e);
                }
            } else if (absY > minSwipeDistance && absY > absX) {
                // Vertical swipe
                if (deltaY > 0) {
                    this.handleSwipeDown(e);
                } else {
                    this.handleSwipeUp(e);
                }
            }

            startX = startY = null;
        }, { passive: true });
    }

    /**
     * Handle swipe gestures
     */
    handleSwipeLeft(e) {
        // Swipe left to open sidebar on mobile
        if (this.isMobile) {
            const sidebar = document.querySelector('.col-lg-4');
            if (sidebar) {
                this.toggleMobileSidebar(true);
            }
        }
    }

    handleSwipeRight(e) {
        // Swipe right to close sidebar on mobile
        if (this.isMobile) {
            this.toggleMobileSidebar(false);
        }
    }

    handleSwipeUp(e) {
        // Swipe up to focus message input
        if (this.isMobile) {
            const messageInput = document.getElementById('messageInput');
            if (messageInput && !messageInput.matches(':focus')) {
                messageInput.focus();
                messageInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }

    handleSwipeDown(e) {
        // Swipe down to refresh (if at top of chat)
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages && chatMessages.scrollTop === 0) {
            this.triggerRefresh();
        }
    }

    /**
     * Setup pull-to-refresh functionality
     */
    setupPullToRefresh() {
        if (!this.isMobile) return;

        let startY = 0;
        let pullDistance = 0;
        const maxPullDistance = 100;
        const refreshThreshold = 60;

        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        chatMessages.addEventListener('touchstart', (e) => {
            if (chatMessages.scrollTop === 0) {
                startY = e.touches[0].clientY;
            }
        }, { passive: true });

        chatMessages.addEventListener('touchmove', (e) => {
            if (startY && chatMessages.scrollTop === 0) {
                pullDistance = Math.min(e.touches[0].clientY - startY, maxPullDistance);
                
                if (pullDistance > 0) {
                    e.preventDefault();
                    this.updatePullIndicator(pullDistance, refreshThreshold);
                }
            }
        });

        chatMessages.addEventListener('touchend', () => {
            if (pullDistance > refreshThreshold) {
                this.triggerRefresh();
            }
            this.resetPullIndicator();
            startY = 0;
            pullDistance = 0;
        }, { passive: true });
    }

    /**
     * Update pull-to-refresh indicator
     */
    updatePullIndicator(distance, threshold) {
        let indicator = document.getElementById('pullRefreshIndicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'pullRefreshIndicator';
            indicator.className = 'text-center p-2';
            indicator.style.position = 'absolute';
            indicator.style.top = '0';
            indicator.style.left = '0';
            indicator.style.right = '0';
            indicator.style.background = 'rgba(255, 255, 255, 0.9)';
            indicator.style.zIndex = '1000';
            indicator.style.transform = 'translateY(-100%)';
            indicator.style.transition = 'transform 0.2s ease';
            
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.style.position = 'relative';
            chatMessages.appendChild(indicator);
        }

        const progress = Math.min(distance / threshold, 1);
        indicator.style.transform = `translateY(${-100 + (progress * 100)}%)`;
        
        if (distance > threshold) {
            indicator.innerHTML = '<i class="bx bx-refresh"></i> Release to refresh';
            indicator.style.color = '#28a745';
        } else {
            indicator.innerHTML = '<i class="bx bx-down-arrow-alt"></i> Pull to refresh';
            indicator.style.color = '#6c757d';
        }
    }

    /**
     * Reset pull-to-refresh indicator
     */
    resetPullIndicator() {
        const indicator = document.getElementById('pullRefreshIndicator');
        if (indicator) {
            indicator.style.transform = 'translateY(-100%)';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 200);
        }
    }

    /**
     * Trigger refresh action
     */
    triggerRefresh() {
        // Trigger appropriate refresh based on current page
        if (window.location.pathname.includes('geolocation')) {
            if (typeof loadCurrentLocations === 'function') {
                loadCurrentLocations();
                loadGeofences();
            }
        } else if (window.location.pathname.includes('ai')) {
            if (typeof loadRecentConversations === 'function') {
                loadRecentConversations();
            }
        }
        
        // Show feedback
        if (typeof toastr !== 'undefined') {
            toastr.success('Refreshed');
        }
    }

    /**
     * Setup responsive features
     */
    setupResponsiveFeatures() {
        // Auto-hide address bar on mobile scroll
        if (this.isMobile) {
            let lastScrollTop = 0;
            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down - hide address bar
                    window.scrollTo(0, scrollTop + 1);
                }
                lastScrollTop = scrollTop;
            }, { passive: true });
        }

        // Adjust viewport height for mobile browsers
        this.setViewportHeight();
        window.addEventListener('resize', this.setViewportHeight.bind(this));
    }

    /**
     * Set proper viewport height for mobile browsers
     */
    setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    /**
     * Setup keyboard handling for mobile
     */
    setupKeyboardHandling() {
        if (!this.isMobile) return;

        const messageInput = document.getElementById('messageInput');
        if (!messageInput) return;

        // Handle virtual keyboard
        let initialViewportHeight = window.innerHeight;

        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;

            if (heightDifference > 150) {
                // Keyboard is likely open
                document.body.classList.add('keyboard-open');
                this.adjustForKeyboard(true);
            } else {
                // Keyboard is likely closed
                document.body.classList.remove('keyboard-open');
                this.adjustForKeyboard(false);
            }
        });

        // Prevent zoom on input focus (iOS)
        messageInput.addEventListener('focus', () => {
            if (messageInput.style.fontSize !== '16px') {
                messageInput.style.fontSize = '16px';
            }
        });
    }

    /**
     * Adjust layout for virtual keyboard
     */
    adjustForKeyboard(isOpen) {
        const chatContainer = document.querySelector('.nestko-chat-container');
        const fab = document.querySelector('.nestko-fab');

        if (isOpen) {
            if (chatContainer) {
                chatContainer.style.paddingBottom = '0';
            }
            if (fab) {
                fab.style.bottom = '10px';
            }
        } else {
            if (chatContainer) {
                chatContainer.style.paddingBottom = '';
            }
            if (fab) {
                fab.style.bottom = '20px';
            }
        }
    }

    /**
     * Setup orientation change handling
     */
    setupOrientationHandling() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.setViewportHeight();
                
                // Refresh map if present
                if (typeof nestkoMap !== 'undefined' && nestkoMap) {
                    nestkoMap.resize();
                }
                
                // Scroll to bottom of chat
                const chatMessages = document.getElementById('chatMessages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            }, 100);
        });
    }

    /**
     * Toggle mobile sidebar
     */
    toggleMobileSidebar(show) {
        const sidebar = document.querySelector('.col-lg-4');
        if (!sidebar) return;

        if (show) {
            sidebar.style.position = 'fixed';
            sidebar.style.top = '0';
            sidebar.style.right = '0';
            sidebar.style.width = '80%';
            sidebar.style.height = '100vh';
            sidebar.style.background = '#fff';
            sidebar.style.zIndex = '1050';
            sidebar.style.transform = 'translateX(0)';
            sidebar.style.transition = 'transform 0.3s ease';
            sidebar.style.overflowY = 'auto';
            sidebar.style.boxShadow = '-2px 0 10px rgba(0,0,0,0.1)';
            
            // Add backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'mobile-sidebar-backdrop';
            backdrop.style.position = 'fixed';
            backdrop.style.top = '0';
            backdrop.style.left = '0';
            backdrop.style.width = '100%';
            backdrop.style.height = '100%';
            backdrop.style.background = 'rgba(0,0,0,0.5)';
            backdrop.style.zIndex = '1049';
            backdrop.addEventListener('click', () => this.toggleMobileSidebar(false));
            document.body.appendChild(backdrop);
        } else {
            sidebar.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                sidebar.style.position = '';
                sidebar.style.top = '';
                sidebar.style.right = '';
                sidebar.style.width = '';
                sidebar.style.height = '';
                sidebar.style.background = '';
                sidebar.style.zIndex = '';
                sidebar.style.transform = '';
                sidebar.style.transition = '';
                sidebar.style.overflowY = '';
                sidebar.style.boxShadow = '';
            }, 300);
            
            // Remove backdrop
            const backdrop = document.querySelector('.mobile-sidebar-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }
    }
}

// Initialize mobile enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.nestkoMobile = new NestKoMobile();
});

// Add CSS custom properties support
if (CSS && CSS.supports && CSS.supports('(--foo: red)')) {
    document.documentElement.style.setProperty('--mobile-vh', window.innerHeight + 'px');
}
