@extends('layout')

@section('title')
    {{ get_label('ai_assistant', 'AI Assistant') }}
@endsection

@section('styles')
<link rel="stylesheet" href="{{ asset('assets/css/nestko-responsive.css') }}">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="fw-bold py-3 mb-4">
                <span class="text-muted fw-light">{{ get_label('nestko', 'NestKo') }} /</span>
                {{ get_label('ai_assistant', 'AI Assistant') }}
            </h4>
        </div>
        <div>
            <button type="button" class="btn btn-outline-primary" id="clearHistoryBtn">
                <i class="bx bx-trash"></i> {{ get_label('clear_history', 'Clear History') }}
            </button>
            <button type="button" class="btn btn-primary" id="newConversationBtn">
                <i class="bx bx-plus"></i> {{ get_label('new_conversation', 'New Conversation') }}
            </button>
        </div>
    </div>

    <!-- Feature Check -->
    @if(!isNestkoFeatureEnabled('ai_assistant'))
        <div class="alert alert-warning">
            <i class="bx bx-info-circle"></i>
            {{ get_label('ai_feature_disabled', 'AI features are not enabled. Please contact your administrator.') }}
        </div>
    @else
        <div class="row">
            <!-- Chat Interface -->
            <div class="col-lg-8 col-md-12">
                <div class="nestko-feature-card">
                    <div class="nestko-chat-container">
                        <div class="nestko-chat-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-0">
                                        <i class="bx bx-brain me-2"></i>
                                        <span class="d-none d-md-inline">{{ get_label('ai_project_assistant', 'AI Project Assistant') }}</span>
                                        <span class="d-md-none">{{ get_label('ai_assistant', 'AI Assistant') }}</span>
                                    </h5>
                                    <small class="opacity-75 d-none d-md-block">{{ get_label('ai_assistant_subtitle', 'Powered by OpenRouter GLM-4.5-Air') }}</small>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-light dropdown-toggle nestko-touch-target" type="button" data-bs-toggle="dropdown">
                                        <i class="bx bx-cog"></i>
                                        <span class="d-none d-md-inline ms-1">{{ get_label('context', 'Context') }}</span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item context-option" href="#" data-context="general">
                                            <i class="bx bx-chat me-2"></i>{{ get_label('general', 'General') }}
                                        </a></li>
                                        <li><a class="dropdown-item context-option" href="#" data-context="project">
                                            <i class="bx bx-folder me-2"></i>{{ get_label('project', 'Project') }}
                                        </a></li>
                                        <li><a class="dropdown-item context-option" href="#" data-context="task">
                                            <i class="bx bx-task me-2"></i>{{ get_label('task', 'Task') }}
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Messages -->
                        <div id="chatMessages" class="nestko-chat-messages">
                            <div class="text-center text-muted py-5">
                                <i class="bx bx-brain display-4"></i>
                                <p class="mt-3">{{ get_label('ai_assistant_welcome', 'Hello! I\'m your AI project assistant. How can I help you today?') }}</p>

                                <!-- Mobile Quick Start -->
                                <div class="d-lg-none mt-4">
                                    <div class="nestko-quick-actions">
                                        <button type="button" class="nestko-quick-action quick-start-action" data-message="Help me organize my tasks for today">
                                            <i class="bx bx-list-check"></i>
                                            {{ get_label('organize_tasks', 'Organize Tasks') }}
                                        </button>
                                        <button type="button" class="nestko-quick-action quick-start-action" data-message="What should I prioritize in my current projects?">
                                            <i class="bx bx-target-lock"></i>
                                            {{ get_label('prioritize_work', 'Prioritize Work') }}
                                        </button>
                                        <button type="button" class="nestko-quick-action quick-start-action" data-message="Give me a summary of my team's progress">
                                            <i class="bx bx-group"></i>
                                            {{ get_label('team_progress', 'Team Progress') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Input -->
                        <div class="nestko-chat-input">
                            <form id="chatForm">
                                <div class="d-flex gap-2 mb-2">
                                    <input type="text"
                                           id="messageInput"
                                           class="form-control"
                                           placeholder="{{ get_label('type_message', 'Type your message...') }}"
                                           maxlength="2000"
                                           required>
                                    <button type="submit" class="btn btn-primary nestko-touch-target" id="sendBtn">
                                        <i class="bx bx-send"></i>
                                    </button>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <span id="currentContext">{{ get_label('context', 'Context') }}: <strong>General</strong></span>
                                    </small>
                                    <small class="text-muted">
                                        <span id="tokenCount">0/2000</span>
                                    </small>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4 col-md-12">
                <!-- Desktop Quick Actions -->
                <div class="nestko-collapsible mb-3 d-none d-lg-block">
                    <div class="nestko-collapsible-header">
                        <span>
                            <i class="bx bx-zap me-2"></i>
                            {{ get_label('quick_actions', 'Quick Actions') }}
                        </span>
                        <i class="bx bx-chevron-down"></i>
                    </div>
                    <div class="nestko-collapsible-content" style="display: block;">
                        <div class="nestko-quick-actions">
                            <button type="button" class="nestko-quick-action quick-action" data-action="summarize">
                                <i class="bx bx-file-blank"></i>
                                {{ get_label('summarize_task', 'Summarize Task') }}
                            </button>
                            <button type="button" class="nestko-quick-action quick-action" data-action="suggestions">
                                <i class="bx bx-bulb"></i>
                                {{ get_label('get_suggestions', 'Get Smart Suggestions') }}
                            </button>
                            <button type="button" class="nestko-quick-action quick-action" data-action="help">
                                <i class="bx bx-help-circle"></i>
                                {{ get_label('help', 'Help & Tips') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Quick Actions Panel -->
                <div class="nestko-collapsible mb-3 d-lg-none">
                    <div class="nestko-collapsible-header" data-bs-toggle="collapse" data-bs-target="#mobileQuickActions">
                        <span>
                            <i class="bx bx-zap me-2"></i>
                            {{ get_label('quick_actions', 'Quick Actions') }}
                        </span>
                        <i class="bx bx-chevron-down"></i>
                    </div>
                    <div class="collapse" id="mobileQuickActions">
                        <div class="nestko-collapsible-content">
                            <div class="nestko-quick-actions">
                                <button type="button" class="nestko-quick-action quick-action" data-action="summarize">
                                    <i class="bx bx-file-blank"></i>
                                    {{ get_label('summarize_task', 'Summarize Task') }}
                                </button>
                                <button type="button" class="nestko-quick-action quick-action" data-action="suggestions">
                                    <i class="bx bx-bulb"></i>
                                    {{ get_label('get_suggestions', 'Get Smart Suggestions') }}
                                </button>
                                <button type="button" class="nestko-quick-action quick-action" data-action="help">
                                    <i class="bx bx-help-circle"></i>
                                    {{ get_label('help', 'Help & Tips') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Conversations -->
                <div class="nestko-collapsible">
                    <div class="nestko-collapsible-header" data-bs-toggle="collapse" data-bs-target="#recentConversationsCollapse">
                        <span>
                            <i class="bx bx-history me-2"></i>
                            {{ get_label('recent_conversations', 'Recent Conversations') }}
                        </span>
                        <i class="bx bx-chevron-down"></i>
                    </div>
                    <div class="collapse show" id="recentConversationsCollapse">
                        <div class="nestko-collapsible-content">
                            <div id="recentConversations">
                                @if($conversations && $conversations->count() > 0)
                                    @foreach($conversations->take(5) as $conversation)
                                        <div class="nestko-message mb-2 {{ $conversation->role }}" data-conversation-id="{{ $conversation->conversation_id }}">
                                            <div class="nestko-message-avatar">
                                                <i class="bx {{ $conversation->role === 'user' ? 'bx-user' : 'bx-brain' }}"></i>
                                            </div>
                                            <div class="nestko-message-bubble">
                                                <small class="text-muted d-block mb-1">{{ $conversation->created_at->diffForHumans() }}</small>
                                                <p class="mb-0 small">{{ Str::limit($conversation->decrypted_content, 60) }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted text-center">{{ get_label('no_conversations', 'No recent conversations') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Floating Action Button -->
        <button type="button" class="nestko-fab d-lg-none" id="aiMobileFab" title="{{ get_label('voice_input', 'Voice Input') }}">
            <i class="bx bx-microphone"></i>
        </button>
    @endif
</div>

<!-- Task Selection Modal -->
<div class="modal fade" id="taskSelectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ get_label('select_task', 'Select Task') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="taskSearch" placeholder="{{ get_label('search_tasks', 'Search tasks...') }}">
                </div>
                <div id="taskList" style="max-height: 300px; overflow-y: auto;">
                    <!-- Tasks will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Project Selection Modal -->
<div class="modal fade" id="projectSelectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ get_label('select_project', 'Select Project') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="projectSearch" placeholder="{{ get_label('search_projects', 'Search projects...') }}">
                </div>
                <div id="projectList" style="max-height: 300px; overflow-y: auto;">
                    <!-- Projects will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@if(isNestkoFeatureEnabled('ai_assistant'))
<script>
// NestKo AI Assistant Configuration
const AI_CONFIG = {
    routes: {
        chat: '{{ route('nestko.ai.chat') }}',
        summarize: '{{ route('nestko.ai.summarize-task') }}',
        suggestions: '{{ route('nestko.ai.generate-suggestions') }}',
        history: '{{ route('nestko.ai.conversation-history') }}',
        clearHistory: '{{ route('nestko.ai.clear-history') }}'
    },
    csrf: '{{ csrf_token() }}',
    maxMessageLength: 2000
};

let currentContext = 'general';
let currentProjectId = null;
let currentTaskId = null;
let isLoading = false;

// Initialize AI Assistant
document.addEventListener('DOMContentLoaded', function() {
    initializeAIAssistant();
    loadRecentConversations();
    setupResponsiveFeatures();
});

function initializeAIAssistant() {
    // Chat form submission
    document.getElementById('chatForm').addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });

    // Message input character count
    document.getElementById('messageInput').addEventListener('input', function() {
        const length = this.value.length;
        document.getElementById('tokenCount').textContent = `${length}/${AI_CONFIG.maxMessageLength}`;

        // Auto-resize textarea on mobile
        if (window.innerWidth <= 768) {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        }
    });

    // Context selection
    document.querySelectorAll('.context-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            setContext(this.dataset.context);
        });
    });

    // Quick actions
    document.querySelectorAll('.quick-action').forEach(button => {
        button.addEventListener('click', function() {
            handleQuickAction(this.dataset.action);
        });
    });

    // Quick start actions (mobile)
    document.querySelectorAll('.quick-start-action').forEach(button => {
        button.addEventListener('click', function() {
            const message = this.dataset.message;
            document.getElementById('messageInput').value = message;
            sendMessage();
        });
    });

    // Clear history
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');
    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', clearConversationHistory);
    }

    // New conversation
    const newConversationBtn = document.getElementById('newConversationBtn');
    if (newConversationBtn) {
        newConversationBtn.addEventListener('click', startNewConversation);
    }
}

function setupResponsiveFeatures() {
    // Mobile floating action button
    const aiMobileFab = document.getElementById('aiMobileFab');
    if (aiMobileFab) {
        aiMobileFab.addEventListener('click', function() {
            // Toggle between voice input and scroll to input
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                startVoiceInput();
            } else {
                // Fallback: scroll to input and focus
                document.getElementById('messageInput').scrollIntoView({ behavior: 'smooth' });
                document.getElementById('messageInput').focus();
            }
        });
    }

    // Collapsible panels
    document.querySelectorAll('.nestko-collapsible-header').forEach(header => {
        header.addEventListener('click', function() {
            const collapsible = this.closest('.nestko-collapsible');
            const content = collapsible.querySelector('.nestko-collapsible-content');
            const icon = this.querySelector('i:last-child');

            if (content.style.display === 'none' || !content.style.display) {
                content.style.display = 'block';
                collapsible.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                collapsible.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });

    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            // Resize chat container
            const chatMessages = document.getElementById('chatMessages');
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }, 100);
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        // Adjust chat container height on mobile
        if (window.innerWidth <= 768) {
            const chatContainer = document.querySelector('.nestko-chat-container');
            if (chatContainer) {
                const viewportHeight = window.innerHeight;
                const headerHeight = document.querySelector('.nestko-chat-header').offsetHeight;
                const inputHeight = document.querySelector('.nestko-chat-input').offsetHeight;
                const availableHeight = viewportHeight - headerHeight - inputHeight - 100; // 100px for margins

                const chatMessages = document.getElementById('chatMessages');
                chatMessages.style.maxHeight = Math.max(300, availableHeight) + 'px';
            }
        }
    });

    // Swipe gestures for mobile
    let startY = 0;
    let startX = 0;

    document.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
        startX = e.touches[0].clientX;
    });

    document.addEventListener('touchend', function(e) {
        const endY = e.changedTouches[0].clientY;
        const endX = e.changedTouches[0].clientX;
        const diffY = startY - endY;
        const diffX = startX - endX;

        // Swipe up to focus input (mobile)
        if (Math.abs(diffY) > Math.abs(diffX) && diffY > 50 && window.innerWidth <= 768) {
            const messageInput = document.getElementById('messageInput');
            if (messageInput && !messageInput.matches(':focus')) {
                messageInput.focus();
            }
        }
    });
}

function sendMessage() {
    if (isLoading) return;

    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;

    isLoading = true;
    updateSendButton(true);

    // Add user message to chat
    addMessageToChat('user', message);
    messageInput.value = '';
    document.getElementById('tokenCount').textContent = '0/2000';

    // Prepare request data
    const requestData = {
        message: message,
        context_type: currentContext,
        project_id: currentProjectId,
        task_id: currentTaskId,
        _token: AI_CONFIG.csrf
    };

    // Send to AI
    fetch(AI_CONFIG.routes.chat, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': AI_CONFIG.csrf
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addMessageToChat('assistant', data.message, {
                tokens: data.tokens_used,
                responseTime: data.response_time,
                model: data.model
            });
        } else {
            addMessageToChat('system', 'Error: ' + (data.error || 'Failed to get response'));
        }
    })
    .catch(error => {
        console.error('AI Chat Error:', error);
        addMessageToChat('system', 'Error: Failed to communicate with AI service');
    })
    .finally(() => {
        isLoading = false;
        updateSendButton(false);
    });
}

function addMessageToChat(role, content, metadata = null) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `nestko-message ${role}`;

    let metadataHtml = '';
    if (metadata) {
        metadataHtml = `
            <div class="mt-1">
                <small class="text-muted">
                    ${metadata.tokens ? `Tokens: ${metadata.tokens}` : ''}
                    ${metadata.responseTime ? ` • ${metadata.responseTime}ms` : ''}
                    ${metadata.model ? ` • ${metadata.model}` : ''}
                </small>
            </div>
        `;
    }

    messageDiv.innerHTML = `
        <div class="nestko-message-avatar">
            <i class="bx ${role === 'user' ? 'bx-user' : role === 'assistant' ? 'bx-brain' : 'bx-info-circle'}"></i>
        </div>
        <div class="nestko-message-bubble">
            <div>${content.replace(/\n/g, '<br>')}</div>
            ${metadataHtml}
        </div>
    `;

    chatMessages.appendChild(messageDiv);

    // Smooth scroll to bottom
    chatMessages.scrollTo({
        top: chatMessages.scrollHeight,
        behavior: 'smooth'
    });

    // Remove welcome message if it exists
    const welcomeMsg = chatMessages.querySelector('.text-center.text-muted');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    // Add animation for new messages
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';
    setTimeout(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    }, 10);
}

function updateSendButton(loading) {
    const sendBtn = document.getElementById('sendBtn');
    const messageInput = document.getElementById('messageInput');
    
    if (loading) {
        sendBtn.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        sendBtn.disabled = true;
        messageInput.disabled = true;
    } else {
        sendBtn.innerHTML = '<i class="bx bx-send"></i>';
        sendBtn.disabled = false;
        messageInput.disabled = false;
        messageInput.focus();
    }
}

function setContext(context) {
    currentContext = context;
    document.getElementById('currentContext').innerHTML = `{{ get_label('context', 'Context') }}: <strong>${context.charAt(0).toUpperCase() + context.slice(1)}</strong>`;
    
    // Reset project/task selection when changing context
    if (context !== 'project') currentProjectId = null;
    if (context !== 'task') currentTaskId = null;
}

function handleQuickAction(action) {
    switch (action) {
        case 'summarize':
            // Show task selection modal
            showTaskSelectionModal();
            break;
        case 'suggestions':
            // Show project/task selection for suggestions
            showSuggestionModal();
            break;
        case 'help':
            addMessageToChat('assistant', `Here are some things I can help you with:

• **Task Management**: Create, update, and organize your tasks
• **Project Planning**: Help with project timelines and milestones  
• **Team Coordination**: Suggest optimal task assignments
• **Productivity Tips**: Provide recommendations for better workflow
• **Smart Summaries**: Summarize long task descriptions
• **Deadline Suggestions**: Recommend realistic deadlines based on complexity

Just ask me anything about your projects and tasks!`);
            break;
    }
}

function loadRecentConversations() {
    fetch(AI_CONFIG.routes.history + '?limit=5')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.conversations.length > 0) {
                updateRecentConversations(data.conversations);
            }
        })
        .catch(error => console.error('Failed to load conversations:', error));
}

function updateRecentConversations(conversations) {
    const container = document.getElementById('recentConversations');
    container.innerHTML = '';

    conversations.forEach(conversation => {
        const div = document.createElement('div');
        div.className = 'mb-2 p-2 border rounded conversation-item';
        div.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="bx ${conversation.role === 'user' ? 'bx-user' : 'bx-brain'} me-2 mt-1"></i>
                <div class="flex-grow-1">
                    <small class="text-muted">${new Date(conversation.created_at).toLocaleDateString()}</small>
                    <p class="mb-0 small">${conversation.decrypted_content.substring(0, 80)}...</p>
                </div>
            </div>
        `;
        container.appendChild(div);
    });
}

function clearConversationHistory() {
    if (!confirm('{{ get_label('confirm_clear_history', 'Are you sure you want to clear your conversation history?') }}')) {
        return;
    }

    fetch(AI_CONFIG.routes.clearHistory, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': AI_CONFIG.csrf
        },
        body: JSON.stringify({
            context_type: currentContext
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('recentConversations').innerHTML = '<p class="text-muted text-center">{{ get_label('no_conversations', 'No recent conversations') }}</p>';
            toastr.success('{{ get_label('history_cleared', 'Conversation history cleared') }}');
        } else {
            toastr.error('{{ get_label('failed_clear_history', 'Failed to clear history') }}');
        }
    })
    .catch(error => {
        console.error('Clear history error:', error);
        toastr.error('{{ get_label('failed_clear_history', 'Failed to clear history') }}');
    });
}

function startNewConversation() {
    document.getElementById('chatMessages').innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="bx bx-brain display-4"></i>
            <p class="mt-3">{{ get_label('ai_assistant_welcome', 'Hello! I\'m your AI project assistant. How can I help you today?') }}</p>
        </div>
    `;
    document.getElementById('messageInput').focus();
}

// Voice input functionality
function startVoiceInput() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        toastr.error('{{ get_label('voice_not_supported', 'Voice input is not supported in this browser') }}');
        return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    const fab = document.getElementById('aiMobileFab');
    const originalIcon = fab.innerHTML;

    // Update FAB to show recording state
    fab.innerHTML = '<i class="bx bx-stop"></i>';
    fab.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';

    recognition.onstart = function() {
        toastr.info('{{ get_label('listening', 'Listening...') }}');
    };

    recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        document.getElementById('messageInput').value = transcript;
        document.getElementById('tokenCount').textContent = `${transcript.length}/${AI_CONFIG.maxMessageLength}`;

        // Auto-send if transcript is long enough
        if (transcript.length > 10) {
            setTimeout(() => sendMessage(), 500);
        }
    };

    recognition.onerror = function(event) {
        toastr.error('{{ get_label('voice_error', 'Voice recognition error') }}: ' + event.error);
    };

    recognition.onend = function() {
        // Restore FAB
        fab.innerHTML = originalIcon;
        fab.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    };

    recognition.start();
}

// Mobile-specific helper functions
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTo({
        top: chatMessages.scrollHeight,
        behavior: 'smooth'
    });
}

function toggleMobilePanel(panelId) {
    const panel = document.getElementById(panelId);
    const isVisible = panel.style.display !== 'none';

    // Hide all panels first
    document.querySelectorAll('.mobile-panel').forEach(p => {
        p.style.display = 'none';
    });

    // Show requested panel if it wasn't visible
    if (!isVisible) {
        panel.style.display = 'block';
    }
}

function adaptToScreenSize() {
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;

    // Adjust chat container for different screen sizes
    const chatContainer = document.querySelector('.nestko-chat-container');
    if (chatContainer) {
        if (isMobile) {
            chatContainer.style.height = '100vh';
            chatContainer.style.maxHeight = 'none';
        } else if (isTablet) {
            chatContainer.style.height = '70vh';
            chatContainer.style.maxHeight = '700px';
        } else {
            chatContainer.style.height = 'auto';
            chatContainer.style.maxHeight = '800px';
        }
    }

    // Adjust message input for mobile
    const messageInput = document.getElementById('messageInput');
    if (messageInput && isMobile) {
        messageInput.style.fontSize = '16px'; // Prevent zoom on iOS
    }
}

// Placeholder functions for modals (to be implemented)
function showTaskSelectionModal() {
    // TODO: Implement task selection modal
    toastr.info('Task selection feature coming soon!');
}

function showSuggestionModal() {
    // TODO: Implement suggestion modal
    toastr.info('Smart suggestions feature coming soon!');
}

// Initialize responsive features on load
window.addEventListener('load', adaptToScreenSize);
window.addEventListener('resize', adaptToScreenSize);
</script>
@endif
@endsection
