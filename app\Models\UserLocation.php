<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'workspace_id',
        'latitude',
        'longitude',
        'accuracy',
        'altitude',
        'heading',
        'speed',
        'address',
        'metadata',
        'is_active',
        'recorded_at',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'accuracy' => 'decimal:2',
        'altitude' => 'decimal:2',
        'heading' => 'decimal:2',
        'speed' => 'decimal:2',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'recorded_at' => 'datetime',
    ];

    /**
     * Get the user that owns the location
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the workspace this location belongs to
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Scope to get current/active locations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get locations within a time range
     */
    public function scopeWithinTimeRange($query, $start, $end)
    {
        return $query->whereBetween('recorded_at', [$start, $end]);
    }

    /**
     * Scope to get locations within a radius of a point
     */
    public function scopeWithinRadius($query, $latitude, $longitude, $radiusMeters)
    {
        $radiusKm = $radiusMeters / 1000;

        return $query->selectRaw("
            *,
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$latitude, $longitude, $latitude])
        ->having('distance', '<=', $radiusKm);
    }

    /**
     * Calculate distance to another location in meters
     */
    public function distanceTo($latitude, $longitude): float
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $latFrom = deg2rad($this->latitude);
        $lonFrom = deg2rad($this->longitude);
        $latTo = deg2rad($latitude);
        $lonTo = deg2rad($longitude);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get formatted address or coordinates
     */
    public function getDisplayLocationAttribute(): string
    {
        return $this->address ?: "{$this->latitude}, {$this->longitude}";
    }

    /**
     * Check if location is stale (older than configured interval)
     */
    public function isStale(): bool
    {
        $interval = getNestkoConfig('geolocation.tracking.interval', 30000) / 1000; // Convert to seconds
        return $this->recorded_at->diffInSeconds(now()) > ($interval * 2);
    }
}
