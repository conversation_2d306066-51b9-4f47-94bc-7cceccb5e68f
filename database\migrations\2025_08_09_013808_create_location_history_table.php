<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('location_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');

            // Location data (encrypted for privacy)
            $table->text('encrypted_latitude'); // Encrypted coordinates
            $table->text('encrypted_longitude');
            $table->decimal('accuracy', 8, 2)->nullable();
            $table->string('address_hash')->nullable(); // Hashed address for privacy

            // Context information
            $table->enum('event_type', ['check_in', 'check_out', 'task_start', 'task_complete', 'geofence_enter', 'geofence_exit', 'manual_update']);
            $table->foreignId('task_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('project_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('geofence_id')->nullable()->constrained()->onDelete('set null');

            // Audit trail
            $table->string('device_id')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->json('metadata')->nullable(); // Additional context

            // Tamper protection
            $table->string('hash'); // Hash of record for integrity
            $table->timestamp('recorded_at');
            $table->timestamps();

            // Indexes for audit queries
            $table->index(['user_id', 'recorded_at']);
            $table->index(['workspace_id', 'recorded_at']);
            $table->index(['task_id', 'event_type']);
            $table->index(['project_id', 'event_type']);
            $table->index('event_type');
            $table->index('hash');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('location_history');
    }
};
