<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkspaceSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'key',
        'value',
        'updated_by',
        'is_encrypted',
    ];

    protected $casts = [
        'is_encrypted' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the workspace that owns the setting
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Get the user who last updated this setting
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get decrypted value if encrypted
     */
    public function getDecryptedValueAttribute(): string
    {
        if ($this->is_encrypted) {
            try {
                return decrypt($this->value);
            } catch (\Exception $e) {
                return '[Decryption failed]';
            }
        }

        return $this->value;
    }

    /**
     * Set encrypted value
     */
    public function setEncryptedValue(string $value): void
    {
        $this->value = encrypt($value);
        $this->is_encrypted = true;
    }

    /**
     * Scope to get settings by key prefix
     */
    public function scopeByKeyPrefix($query, string $prefix)
    {
        return $query->where('key', 'like', $prefix . '%');
    }

    /**
     * Get setting value with default
     */
    public static function getValue(int $workspaceId, string $key, $default = null)
    {
        $setting = static::where('workspace_id', $workspaceId)
            ->where('key', $key)
            ->first();

        if (!$setting) {
            return $default;
        }

        return $setting->is_encrypted ? $setting->decrypted_value : $setting->value;
    }

    /**
     * Set setting value
     */
    public static function setValue(int $workspaceId, string $key, $value, bool $encrypt = false, int $updatedBy = null): void
    {
        $setting = static::updateOrCreate(
            [
                'workspace_id' => $workspaceId,
                'key' => $key
            ],
            [
                'value' => $encrypt ? encrypt($value) : $value,
                'is_encrypted' => $encrypt,
                'updated_by' => $updatedBy ?: getAuthenticatedUser()?->id
            ]
        );
    }
}
