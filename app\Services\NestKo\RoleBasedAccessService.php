<?php

namespace App\Services\NestKo;

use App\Models\User;
use App\Models\Workspace;

class RoleBasedAccessService
{
    /**
     * Define role-based access levels for NestKo features
     */
    protected static $rolePermissions = [
        'superadmin' => [
            'geolocation' => [
                'access' => true,
                'features' => ['view', 'configure', 'manage_global_settings', 'view_all_workspaces', 'export_data', 'analytics'],
                'api_access' => false, // Super admin configures but doesn't use API directly
                'settings_access' => 'full'
            ],
            'ai_assistant' => [
                'access' => true,
                'features' => ['view', 'configure', 'manage_global_settings', 'view_all_conversations', 'analytics', 'cost_management'],
                'api_access' => false,
                'settings_access' => 'full'
            ]
        ],
        'manager' => [
            'geolocation' => [
                'access' => true,
                'features' => ['view', 'configure_workspace', 'manage_team', 'reports'],
                'api_access' => false,
                'settings_access' => 'workspace'
            ],
            'ai_assistant' => [
                'access' => true,
                'features' => ['view', 'chat', 'team_analytics', 'workspace_settings'],
                'api_access' => true,
                'settings_access' => 'workspace'
            ]
        ],
        'admin' => [
            'geolocation' => [
                'access' => true,
                'features' => ['view', 'configure_workspace', 'manage_team', 'basic_reports'],
                'api_access' => true, // Workspace admin can configure API
                'settings_access' => 'workspace'
            ],
            'ai_assistant' => [
                'access' => true,
                'features' => ['view', 'chat', 'team_management', 'basic_analytics'],
                'api_access' => true,
                'settings_access' => 'workspace'
            ]
        ],
        'member' => [
            'geolocation' => [
                'access' => true,
                'features' => ['view_own', 'share_location', 'view_team'],
                'api_access' => false,
                'settings_access' => 'personal'
            ],
            'ai_assistant' => [
                'access' => true,
                'features' => ['chat', 'personal_assistant', 'task_help'],
                'api_access' => true,
                'settings_access' => 'personal'
            ]
        ],
        'client' => [
            'geolocation' => [
                'access' => false, // Clients don't have geolocation access by default
                'features' => [],
                'api_access' => false,
                'settings_access' => 'none'
            ],
            'ai_assistant' => [
                'access' => true,
                'features' => ['chat', 'project_help'], // Limited AI access
                'api_access' => false,
                'settings_access' => 'none'
            ]
        ]
    ];

    /**
     * Check if user can access a specific NestKo feature
     */
    public static function canAccessFeature(string $feature, User $user = null): bool
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        if (!$user) {
            return false;
        }

        $userRole = self::getUserPrimaryRole($user);
        
        return self::$rolePermissions[$userRole][$feature]['access'] ?? false;
    }

    /**
     * Get available features for user role
     */
    public static function getAvailableFeatures(string $feature, User $user = null): array
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        if (!$user) {
            return [];
        }

        $userRole = self::getUserPrimaryRole($user);
        
        return self::$rolePermissions[$userRole][$feature]['features'] ?? [];
    }

    /**
     * Check if user can access API for a feature
     */
    public static function canAccessAPI(string $feature, User $user = null): bool
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        if (!$user) {
            return false;
        }

        $userRole = self::getUserPrimaryRole($user);
        
        return self::$rolePermissions[$userRole][$feature]['api_access'] ?? false;
    }

    /**
     * Get settings access level for user
     */
    public static function getSettingsAccess(string $feature, User $user = null): string
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        if (!$user) {
            return 'none';
        }

        $userRole = self::getUserPrimaryRole($user);
        
        return self::$rolePermissions[$userRole][$feature]['settings_access'] ?? 'none';
    }

    /**
     * Check if user has specific feature permission
     */
    public static function hasFeaturePermission(string $feature, string $permission, User $user = null): bool
    {
        $availableFeatures = self::getAvailableFeatures($feature, $user);
        
        return in_array($permission, $availableFeatures);
    }

    /**
     * Get user's primary role (highest priority role)
     */
    protected static function getUserPrimaryRole(User $user): string
    {
        // Role hierarchy (highest to lowest priority)
        $roleHierarchy = ['superadmin', 'manager', 'admin', 'member', 'client'];
        
        foreach ($roleHierarchy as $role) {
            if ($user->hasRole($role)) {
                return $role;
            }
        }
        
        return 'member'; // Default fallback
    }

    /**
     * Get role-based menu items for NestKo features
     */
    public static function getRoleBasedMenuItems(User $user = null): array
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        if (!$user) {
            return [];
        }

        $menuItems = [];
        $userRole = self::getUserPrimaryRole($user);

        // Geolocation menu item
        if (self::canAccessFeature('geolocation', $user)) {
            $menuItems[] = [
                'id' => 'nestko_geolocation',
                'label' => get_label('geolocation_intelligence', 'Geolocation Intelligence'),
                'url' => route('nestko.geolocation.index'),
                'icon' => 'bx bx-map',
                'class' => 'menu-item' . (Request::is('nestko/geolocation*') ? ' active' : ''),
                'show' => 1,
                'category' => get_label('nestko_suite', 'NestKo Suite'),
                'role_access' => $userRole,
                'features' => self::getAvailableFeatures('geolocation', $user)
            ];
        }

        // AI Assistant menu item
        if (self::canAccessFeature('ai_assistant', $user)) {
            $menuItems[] = [
                'id' => 'nestko_ai',
                'label' => get_label('ai_productivity_suite', 'AI Productivity Suite'),
                'url' => route('nestko.ai.assistant'),
                'icon' => 'bx bx-brain',
                'class' => 'menu-item' . (Request::is('nestko/ai*') ? ' active' : ''),
                'show' => 1,
                'category' => get_label('nestko_suite', 'NestKo Suite'),
                'role_access' => $userRole,
                'features' => self::getAvailableFeatures('ai_assistant', $user)
            ];
        }

        return $menuItems;
    }

    /**
     * Get settings menu items based on role
     */
    public static function getSettingsMenuItems(User $user = null): array
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        if (!$user) {
            return [];
        }

        $menuItems = [];
        $userRole = self::getUserPrimaryRole($user);

        // Super Admin Settings
        if ($userRole === 'superadmin') {
            $menuItems[] = [
                'id' => 'nestko_global_settings',
                'label' => get_label('nestko_global_settings', 'NestKo Global Settings'),
                'url' => route('superadmin.nestko.settings'),
                'icon' => 'bx bx-cog',
                'class' => 'menu-item',
                'show' => 1,
                'category' => get_label('system_settings', 'System Settings')
            ];
        }

        // Workspace Admin Settings
        if (in_array($userRole, ['admin', 'manager'])) {
            if (self::getSettingsAccess('geolocation', $user) === 'workspace') {
                $menuItems[] = [
                    'id' => 'nestko_workspace_settings',
                    'label' => get_label('nestko_workspace_settings', 'NestKo Workspace Settings'),
                    'url' => route('nestko.workspace.settings'),
                    'icon' => 'bx bx-cog',
                    'class' => 'menu-item',
                    'show' => 1,
                    'category' => get_label('workspace_settings', 'Workspace Settings')
                ];
            }
        }

        return $menuItems;
    }

    /**
     * Validate feature access and throw exception if unauthorized
     */
    public static function validateFeatureAccess(string $feature, string $permission = null, User $user = null): void
    {
        if (!self::canAccessFeature($feature, $user)) {
            abort(403, "Access denied: You don't have permission to access {$feature} features.");
        }

        if ($permission && !self::hasFeaturePermission($feature, $permission, $user)) {
            abort(403, "Access denied: You don't have permission for {$permission} in {$feature}.");
        }
    }

    /**
     * Get role-based feature configuration
     */
    public static function getRoleConfiguration(string $role): array
    {
        return self::$rolePermissions[$role] ?? [];
    }

    /**
     * Check if user can manage workspace settings
     */
    public static function canManageWorkspaceSettings(User $user = null): bool
    {
        if (!$user) {
            $user = getAuthenticatedUser();
        }

        $userRole = self::getUserPrimaryRole($user);
        
        return in_array($userRole, ['admin', 'manager', 'superadmin']);
    }

    /**
     * Check if user can view analytics
     */
    public static function canViewAnalytics(string $feature, User $user = null): bool
    {
        return self::hasFeaturePermission($feature, 'analytics', $user) || 
               self::hasFeaturePermission($feature, 'basic_analytics', $user) ||
               self::hasFeaturePermission($feature, 'team_analytics', $user);
    }
}
