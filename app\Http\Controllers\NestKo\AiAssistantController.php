<?php

namespace App\Http\Controllers\NestKo;

use App\Http\Controllers\Controller;
use App\Services\NestKo\OpenRouterService;
use App\Services\NestKo\RoleBasedAccessService;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use App\Models\AiSuggestion;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class AiAssistantController extends Controller
{
    protected $openRouterService;

    public function __construct(OpenRouterService $openRouterService)
    {
        $this->openRouterService = $openRouterService;

        // Check role-based feature access
        $this->middleware(function ($request, $next) {
            $user = getAuthenticatedUser();
            RoleBasedAccessService::validateFeatureAccess('ai_assistant', null, $user);
            return $next($request);
        });
    }

    /**
     * Display the AI assistant interface
     */
    public function index(): View
    {
        $user = getAuthenticatedUser();
        $workspaceId = getWorkspaceId();

        // Get recent conversations
        $conversations = $this->openRouterService->getConversationHistory(
            $user->id,
            $workspaceId,
            'general',
            20
        );

        return view('nestko.ai.assistant.index', compact('conversations'));
    }

    /**
     * Handle chat messages with AI assistant
     */
    public function chat(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'required|string|max:2000',
            'context_type' => 'nullable|string|in:general,project,task,summary',
            'project_id' => 'nullable|exists:projects,id',
            'task_id' => 'nullable|exists:tasks,id',
        ]);

        try {
            $user = getAuthenticatedUser();
            $workspaceId = getWorkspaceId();

            // Build context based on request
            $context = $this->buildChatContext($request, $user, $workspaceId);

            // Generate AI response
            $response = $this->openRouterService->generateProjectAssistantResponse(
                $user->id,
                $workspaceId,
                $request->message,
                $context
            );

            return response()->json([
                'success' => true,
                'message' => $response['choices'][0]['message']['content'] ?? 'No response generated',
                'tokens_used' => $response['usage']['total_tokens'] ?? 0,
                'response_time' => $response['response_time_ms'] ?? 0,
                'model' => $response['model_used'] ?? 'unknown'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate task summary using AI
     */
    public function summarizeTask(Request $request): JsonResponse
    {
        $request->validate([
            'task_id' => 'required|exists:tasks,id',
        ]);

        try {
            $task = Task::findOrFail($request->task_id);

            // Check if user has access to this task
            if (!$this->userCanAccessTask($task)) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $context = [
                'project' => $task->project ? $task->project->title : null,
                'priority' => $task->priority ? $task->priority->title : null,
                'status' => $task->status ? $task->status->title : null,
                'assigned_users' => $task->users->pluck('first_name', 'last_name')->toArray(),
            ];

            $response = $this->openRouterService->generateTaskSummary(
                $task->description ?? $task->title,
                $context
            );

            $summary = $response['choices'][0]['message']['content'] ?? 'Unable to generate summary';

            return response()->json([
                'success' => true,
                'summary' => $summary,
                'tokens_used' => $response['usage']['total_tokens'] ?? 0,
                'original_length' => strlen($task->description ?? $task->title),
                'summary_length' => strlen($summary)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate smart suggestions for tasks or projects
     */
    public function generateSuggestions(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:task,project',
            'target_id' => 'required|integer',
        ]);

        try {
            $user = getAuthenticatedUser();
            $workspaceId = getWorkspaceId();

            if ($request->type === 'task') {
                $task = Task::findOrFail($request->target_id);

                if (!$this->userCanAccessTask($task)) {
                    return response()->json(['error' => 'Unauthorized'], 403);
                }

                $data = $this->buildTaskDataForSuggestions($task);
                $targetType = 'task';
                $targetId = $task->id;
            } else {
                $project = Project::findOrFail($request->target_id);

                if (!$this->userCanAccessProject($project)) {
                    return response()->json(['error' => 'Unauthorized'], 403);
                }

                $data = $this->buildProjectDataForSuggestions($project);
                $targetType = 'project';
                $targetId = $project->id;
            }

            // Generate AI suggestions
            $response = $this->openRouterService->generateSmartSuggestions($request->type, $data);
            $suggestionsText = $response['choices'][0]['message']['content'] ?? '';

            // Parse suggestions (assuming JSON format from AI)
            try {
                $suggestions = json_decode($suggestionsText, true);
                if (!$suggestions) {
                    $suggestions = ['general' => $suggestionsText];
                }
            } catch (\Exception $e) {
                $suggestions = ['general' => $suggestionsText];
            }

            // Store suggestions in database
            $aiSuggestion = AiSuggestion::create([
                'workspace_id' => $workspaceId,
                'generated_by' => $user->id,
                'target_type' => $targetType,
                'target_id' => $targetId,
                'suggestion_type' => 'optimization',
                'title' => 'AI Smart Suggestions',
                'description' => 'AI-generated suggestions for improved productivity',
                'suggestion_data' => $suggestions,
                'ai_model' => $response['model_used'] ?? 'glm4.5-air',
                'confidence_score' => 0.8, // Default confidence
                'reasoning' => 'Generated using AI analysis of current data',
                'tokens_used' => $response['usage']['total_tokens'] ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'suggestions' => $suggestions,
                'suggestion_id' => $aiSuggestion->id,
                'tokens_used' => $response['usage']['total_tokens'] ?? 0
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply AI suggestion
     */
    public function applySuggestion(Request $request): JsonResponse
    {
        $request->validate([
            'suggestion_id' => 'required|exists:ai_suggestions,id',
            'apply_fields' => 'required|array',
        ]);

        try {
            $suggestion = AiSuggestion::findOrFail($request->suggestion_id);

            // Check permissions
            if ($suggestion->workspace_id !== getWorkspaceId()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Apply suggestions based on target type
            $appliedChanges = [];

            if ($suggestion->target_type === 'task') {
                $appliedChanges = $this->applyTaskSuggestions($suggestion, $request->apply_fields);
            } elseif ($suggestion->target_type === 'project') {
                $appliedChanges = $this->applyProjectSuggestions($suggestion, $request->apply_fields);
            }

            // Update suggestion status
            $suggestion->update([
                'status' => 'applied',
                'is_applied' => true,
                'applied_at' => now(),
                'applied_changes' => $appliedChanges,
                'reviewed_by' => getAuthenticatedUser()->id,
                'reviewed_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'applied_changes' => $appliedChanges,
                'message' => 'Suggestions applied successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get conversation history
     */
    public function getConversationHistory(Request $request): JsonResponse
    {
        $request->validate([
            'context_type' => 'nullable|string',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = getAuthenticatedUser();
            $workspaceId = getWorkspaceId();

            $conversations = $this->openRouterService->getConversationHistory(
                $user->id,
                $workspaceId,
                $request->context_type,
                $request->limit ?? 20
            );

            return response()->json([
                'success' => true,
                'conversations' => $conversations
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear conversation history
     */
    public function clearConversationHistory(Request $request): JsonResponse
    {
        $request->validate([
            'context_type' => 'nullable|string',
        ]);

        try {
            $user = getAuthenticatedUser();
            $workspaceId = getWorkspaceId();

            $cleared = $this->openRouterService->clearConversationHistory(
                $user->id,
                $workspaceId,
                $request->context_type
            );

            return response()->json([
                'success' => true,
                'cleared' => $cleared,
                'message' => 'Conversation history cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Build chat context based on request
     */
    protected function buildChatContext(Request $request, User $user, int $workspaceId): array
    {
        $context = [
            'context_type' => $request->context_type ?? 'general',
            'project_id' => $request->project_id,
            'task_id' => $request->task_id,
        ];

        // Add relevant projects and tasks
        $context['projects'] = Project::where('workspace_id', $workspaceId)
            ->whereHas('users', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->select('id', 'title', 'status_id')
            ->limit(5)
            ->get()
            ->toArray();

        $context['tasks'] = Task::where('workspace_id', $workspaceId)
            ->whereHas('users', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->select('id', 'title', 'status_id', 'priority_id')
            ->limit(10)
            ->get()
            ->toArray();

        return $context;
    }

    /**
     * Check if user can access task
     */
    protected function userCanAccessTask(Task $task): bool
    {
        $user = getAuthenticatedUser();

        // Check workspace access
        if ($task->workspace_id !== getWorkspaceId()) {
            return false;
        }

        // Check if user is assigned to task or project
        return $task->users->contains($user->id) ||
               ($task->project && $task->project->users->contains($user->id)) ||
               $user->hasRole('admin');
    }

    /**
     * Check if user can access project
     */
    protected function userCanAccessProject(Project $project): bool
    {
        $user = getAuthenticatedUser();

        // Check workspace access
        if ($project->workspace_id !== getWorkspaceId()) {
            return false;
        }

        // Check if user is assigned to project
        return $project->users->contains($user->id) || $user->hasRole('admin');
    }

    /**
     * Build task data for AI suggestions
     */
    protected function buildTaskDataForSuggestions(Task $task): array
    {
        return [
            'title' => $task->title,
            'description' => $task->description,
            'status' => $task->status ? $task->status->title : null,
            'priority' => $task->priority ? $task->priority->title : null,
            'start_date' => $task->start_date,
            'due_date' => $task->due_date,
            'assigned_users' => $task->users->map(function ($user) {
                return [
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'role' => $user->getRoleNames()->first(),
                ];
            })->toArray(),
            'project' => $task->project ? [
                'title' => $task->project->title,
                'status' => $task->project->status ? $task->project->status->title : null,
            ] : null,
            'completion_percentage' => $task->task_completion_percentage ?? 0,
        ];
    }

    /**
     * Build project data for AI suggestions
     */
    protected function buildProjectDataForSuggestions(Project $project): array
    {
        return [
            'title' => $project->title,
            'description' => $project->description,
            'status' => $project->status ? $project->status->title : null,
            'priority' => $project->priority ? $project->priority->title : null,
            'start_date' => $project->start_date,
            'end_date' => $project->end_date,
            'budget' => $project->budget,
            'team_members' => $project->users->map(function ($user) {
                return [
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'role' => $user->getRoleNames()->first(),
                ];
            })->toArray(),
            'tasks_count' => $project->tasks->count(),
            'completed_tasks' => $project->tasks->where('status.title', 'Done')->count(),
        ];
    }

    /**
     * Apply task suggestions
     */
    protected function applyTaskSuggestions(AiSuggestion $suggestion, array $applyFields): array
    {
        $task = Task::find($suggestion->target_id);
        if (!$task) {
            throw new \Exception('Task not found');
        }

        $appliedChanges = [];
        $suggestions = $suggestion->suggestion_data;

        foreach ($applyFields as $field) {
            if (isset($suggestions[$field])) {
                switch ($field) {
                    case 'deadline':
                    case 'due_date':
                        if (isset($suggestions[$field])) {
                            $task->due_date = $suggestions[$field];
                            $appliedChanges['due_date'] = $suggestions[$field];
                        }
                        break;
                    case 'priority':
                        if (isset($suggestions[$field])) {
                            // Find priority by name or create if needed
                            $priority = \App\Models\Priority::where('title', $suggestions[$field])->first();
                            if ($priority) {
                                $task->priority_id = $priority->id;
                                $appliedChanges['priority'] = $suggestions[$field];
                            }
                        }
                        break;
                }
            }
        }

        $task->save();
        return $appliedChanges;
    }

    /**
     * Apply project suggestions
     */
    protected function applyProjectSuggestions(AiSuggestion $suggestion, array $applyFields): array
    {
        $project = Project::find($suggestion->target_id);
        if (!$project) {
            throw new \Exception('Project not found');
        }

        $appliedChanges = [];
        $suggestions = $suggestion->suggestion_data;

        foreach ($applyFields as $field) {
            if (isset($suggestions[$field])) {
                switch ($field) {
                    case 'end_date':
                        if (isset($suggestions[$field])) {
                            $project->end_date = $suggestions[$field];
                            $appliedChanges['end_date'] = $suggestions[$field];
                        }
                        break;
                    case 'budget':
                        if (isset($suggestions[$field])) {
                            $project->budget = $suggestions[$field];
                            $appliedChanges['budget'] = $suggestions[$field];
                        }
                        break;
                }
            }
        }

        $project->save();
        return $appliedChanges;
    }
}
