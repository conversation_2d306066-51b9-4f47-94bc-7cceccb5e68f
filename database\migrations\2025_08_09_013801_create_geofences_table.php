<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('geofences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['circle', 'polygon'])->default('circle');
            $table->boolean('is_active')->default(true);

            // Circle geofence
            $table->decimal('center_latitude', 10, 8)->nullable();
            $table->decimal('center_longitude', 11, 8)->nullable();
            $table->integer('radius_meters')->nullable();

            // Polygon geofence (stored as JSON array of coordinates)
            $table->json('polygon_coordinates')->nullable();

            // Alert settings
            $table->boolean('alert_on_enter')->default(true);
            $table->boolean('alert_on_exit')->default(true);
            $table->json('alert_users')->nullable(); // User IDs to alert
            $table->json('alert_methods')->nullable(); // push, email, sms
            $table->text('alert_message')->nullable();

            // Associated entities
            $table->foreignId('project_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('task_id')->nullable()->constrained()->onDelete('cascade');

            // Metadata
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['workspace_id', 'is_active']);
            $table->index(['center_latitude', 'center_longitude']);
            $table->index(['project_id', 'task_id']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geofences');
    }
};
