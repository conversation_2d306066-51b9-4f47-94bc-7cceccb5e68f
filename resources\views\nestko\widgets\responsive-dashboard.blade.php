{{-- NestKo Responsive Dashboard Widget --}}
@if(isNestkoFeatureEnabled('geolocation') || isNestkoFeatureEnabled('ai_assistant'))
<div class="row mb-4">
    <div class="col-12">
        <div class="nestko-feature-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bx bx-rocket me-2"></i>
                    {{ get_label('nestko_suite', 'NestKo Suite') }}
                </h5>
                <div class="dropdown d-none d-md-block">
                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bx bx-cog"></i> {{ get_label('settings', 'Settings') }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        @if(isNestkoFeatureEnabled('geolocation'))
                            <li><a class="dropdown-item" href="{{ route('nestko.geolocation.index') }}">
                                <i class="bx bx-map me-2"></i>{{ get_label('geolocation_settings', 'Geolocation Settings') }}
                            </a></li>
                        @endif
                        @if(isNestkoFeatureEnabled('ai_assistant'))
                            <li><a class="dropdown-item" href="{{ route('nestko.ai.assistant') }}">
                                <i class="bx bx-brain me-2"></i>{{ get_label('ai_settings', 'AI Settings') }}
                            </a></li>
                        @endif
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <!-- Mobile Quick Access Grid -->
                <div class="nestko-quick-actions">
                    @if(isNestkoFeatureEnabled('geolocation'))
                        <a href="{{ route('nestko.geolocation.index') }}" class="nestko-quick-action">
                            <i class="bx bx-map"></i>
                            <div>
                                <div class="fw-semibold">{{ get_label('live_tracking', 'Live Tracking') }}</div>
                                <small class="text-muted d-none d-md-block">{{ get_label('track_team_locations', 'Track team locations in real-time') }}</small>
                            </div>
                        </a>
                    @endif
                    
                    @if(isNestkoFeatureEnabled('ai_assistant'))
                        <a href="{{ route('nestko.ai.assistant') }}" class="nestko-quick-action">
                            <i class="bx bx-brain"></i>
                            <div>
                                <div class="fw-semibold">{{ get_label('ai_assistant', 'AI Assistant') }}</div>
                                <small class="text-muted d-none d-md-block">{{ get_label('ai_productivity_help', 'Get AI-powered productivity assistance') }}</small>
                            </div>
                        </a>
                    @endif
                    
                    @if(isNestkoFeatureEnabled('geolocation'))
                        <button type="button" class="nestko-quick-action" onclick="showNearbyTeam()">
                            <i class="bx bx-group"></i>
                            <div>
                                <div class="fw-semibold">{{ get_label('nearby_team', 'Nearby Team') }}</div>
                                <small class="text-muted d-none d-md-block">{{ get_label('find_nearby_members', 'Find team members near you') }}</small>
                            </div>
                        </button>
                    @endif
                    
                    @if(isNestkoFeatureEnabled('ai_assistant'))
                        <button type="button" class="nestko-quick-action" onclick="quickAIHelp()">
                            <i class="bx bx-bulb"></i>
                            <div>
                                <div class="fw-semibold">{{ get_label('smart_suggestions', 'Smart Suggestions') }}</div>
                                <small class="text-muted d-none d-md-block">{{ get_label('ai_task_suggestions', 'Get AI suggestions for your tasks') }}</small>
                            </div>
                        </button>
                    @endif
                </div>

                <!-- Feature Status Indicators (Mobile Optimized) -->
                <div class="row mt-3 d-none d-lg-flex">
                    @if(isNestkoFeatureEnabled('geolocation'))
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="nestko-status-badge live me-2">
                                    <i class="bx bx-wifi"></i>
                                </div>
                                <div>
                                    <small class="fw-semibold">{{ get_label('geolocation_status', 'Geolocation') }}</small>
                                    <br>
                                    <small class="text-muted" id="geoStatus">{{ get_label('checking', 'Checking...') }}</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    @if(isNestkoFeatureEnabled('ai_assistant'))
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="nestko-status-badge live me-2">
                                    <i class="bx bx-brain"></i>
                                </div>
                                <div>
                                    <small class="fw-semibold">{{ get_label('ai_status', 'AI Assistant') }}</small>
                                    <br>
                                    <small class="text-muted" id="aiStatus">{{ get_label('ready', 'Ready') }}</small>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile-Only Quick Stats -->
<div class="row mb-3 d-lg-none">
    @if(isNestkoFeatureEnabled('geolocation'))
        <div class="col-6">
            <div class="nestko-feature-card text-center">
                <div class="card-body py-3">
                    <i class="bx bx-map display-6 text-primary"></i>
                    <div class="mt-2">
                        <div class="fw-semibold" id="activeLocations">-</div>
                        <small class="text-muted">{{ get_label('active_locations', 'Active Locations') }}</small>
                    </div>
                </div>
            </div>
        </div>
    @endif
    
    @if(isNestkoFeatureEnabled('ai_assistant'))
        <div class="col-6">
            <div class="nestko-feature-card text-center">
                <div class="card-body py-3">
                    <i class="bx bx-brain display-6 text-success"></i>
                    <div class="mt-2">
                        <div class="fw-semibold" id="aiConversations">-</div>
                        <small class="text-muted">{{ get_label('ai_conversations', 'AI Conversations') }}</small>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
// NestKo Dashboard Widget Functions
document.addEventListener('DOMContentLoaded', function() {
    initNestkoDashboard();
});

function initNestkoDashboard() {
    @if(isNestkoFeatureEnabled('geolocation'))
        checkGeolocationStatus();
        loadLocationStats();
    @endif
    
    @if(isNestkoFeatureEnabled('ai_assistant'))
        loadAIStats();
    @endif
}

@if(isNestkoFeatureEnabled('geolocation'))
function checkGeolocationStatus() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                document.getElementById('geoStatus').textContent = '{{ get_label('location_enabled', 'Location enabled') }}';
            },
            function(error) {
                document.getElementById('geoStatus').textContent = '{{ get_label('location_disabled', 'Location disabled') }}';
            }
        );
    } else {
        document.getElementById('geoStatus').textContent = '{{ get_label('not_supported', 'Not supported') }}';
    }
}

function loadLocationStats() {
    fetch('{{ route('nestko.geolocation.locations.current') }}')
        .then(response => response.json())
        .then(data => {
            const activeCount = data.length || 0;
            const activeLocationsEl = document.getElementById('activeLocations');
            if (activeLocationsEl) {
                activeLocationsEl.textContent = activeCount;
            }
        })
        .catch(error => {
            console.error('Error loading location stats:', error);
        });
}

function showNearbyTeam() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const data = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                radius_meters: 1000,
                _token: '{{ csrf_token() }}'
            };
            
            fetch('{{ route('nestko.geolocation.users.nearby') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(users => {
                if (users.length > 0) {
                    toastr.success(`Found ${users.length} team member(s) nearby`);
                    // Redirect to map view
                    window.location.href = '{{ route('nestko.geolocation.index') }}';
                } else {
                    toastr.info('No team members found nearby');
                }
            })
            .catch(error => {
                console.error('Error finding nearby team:', error);
                toastr.error('Failed to find nearby team members');
            });
        }, function(error) {
            toastr.error('Location access denied');
        });
    } else {
        toastr.error('Geolocation is not supported');
    }
}
@endif

@if(isNestkoFeatureEnabled('ai_assistant'))
function loadAIStats() {
    fetch('{{ route('nestko.ai.conversation-history') }}?limit=1')
        .then(response => response.json())
        .then(data => {
            const conversationCount = data.conversations ? data.conversations.length : 0;
            const aiConversationsEl = document.getElementById('aiConversations');
            if (aiConversationsEl) {
                aiConversationsEl.textContent = conversationCount > 0 ? '{{ get_label('active', 'Active') }}' : '{{ get_label('ready', 'Ready') }}';
            }
        })
        .catch(error => {
            console.error('Error loading AI stats:', error);
        });
}

function quickAIHelp() {
    // Quick AI help - redirect to AI assistant with a helpful prompt
    const helpMessage = encodeURIComponent('Help me organize my tasks and priorities for today');
    window.location.href = '{{ route('nestko.ai.assistant') }}?message=' + helpMessage;
}
@endif

// Responsive dashboard adjustments
function adjustDashboardForMobile() {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // Hide detailed descriptions on mobile
        document.querySelectorAll('.nestko-quick-action small').forEach(el => {
            el.style.display = 'none';
        });
        
        // Adjust grid layout
        document.querySelectorAll('.nestko-quick-actions').forEach(grid => {
            grid.style.gridTemplateColumns = '1fr';
        });
    } else {
        // Show descriptions on larger screens
        document.querySelectorAll('.nestko-quick-action small').forEach(el => {
            el.style.display = 'block';
        });
        
        // Restore grid layout
        document.querySelectorAll('.nestko-quick-actions').forEach(grid => {
            grid.style.gridTemplateColumns = '';
        });
    }
}

// Handle responsive changes
window.addEventListener('resize', adjustDashboardForMobile);
window.addEventListener('orientationchange', function() {
    setTimeout(adjustDashboardForMobile, 100);
});

// Initial adjustment
adjustDashboardForMobile();
</script>
@endif
