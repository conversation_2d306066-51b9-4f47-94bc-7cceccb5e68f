<!DOCTYPE html>

<html lang="en" class="light-style layout-menu-fixed" dir="ltr" data-theme="theme-default"
    data-assets-path="{{ asset('assets/') }}" data-template="vertical-menu-template-free">

<head>
    <!-- PWA  -->

    <link rel="manifest" href="{{ route('manifest') }}">

    <meta name="theme-color" content="{{ $pwa_settings['pwa_theme_color'] }}">

    <link rel="apple-touch-icon" href="{{ asset($general_settings['full_logo']) }}">

    <meta charset="utf-8" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title>@yield('title') - {{ $general_settings['company_title'] ?? 'Taskify - Saas' }}</title>


    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon"
        href="{{ asset($general_settings['favicon'] ?? 'storage/logos/default_favicon.png') }}" />
    @include('front-end.include-css')
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function () {
                navigator.serviceWorker.register('/sw.js')
                    .then(function (registration) {
                        console.log('Service Worker registered with scope:', registration.scope);
                    }, function (err) {
                        console.log('Service Worker registration failed:', err);
                    });
            });
        }
    </script>
    <script src="{{ asset('assets/vendor/libs/jquery/jquery.js') }}"></script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-NNHYQ5JNBW"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-NNHYQ5JNBW');
    </script>

</head>

<body class="">
    <div class="custom-hero-header-bg-img">
        {{-- 0 Means Demo Mode On --}}
        <?php if (config('constants.ALLOW_MODIFICATION') === 0): ?> 
        <button class="settings-trigger" id="settingsBtn">
            <i class="fas fa-cog"></i>
        </button>
        <?php endif; ?>


        <!-- Settings Drawer -->
        <div class="settings-drawer" id="settingsDrawer">

            <div class="drawer-content">
                <div class="settings-section">
                    <div class="section-title">
                        <i class="fas fa-palette"></i>
                        Theme Preferences
                    </div>  
                    {{-- @dd($active_theme) --}}
                    <div class="theme-options">
                        <div class="theme-option theme-modern switch-theme-btn{{ $active_theme === 'new' ? 'active' : '' }}"
                            data-theme="new">
                            <div class="theme-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="theme-info">
                                <div class="theme-name">Modern Theme</div>
                                <div class="theme-description">Clean, contemporary design with modern elements</div>
                            </div>
                        </div>

                        <div class="theme-option theme-classic switch-theme-btn {{ $active_theme === 'old' ? 'active' : '' }}"
                            data-theme="old">
                            <div class="theme-icon">
                                <i class="fas fa-gem"></i>
                            </div>
                            <div class="theme-info">
                                <div class="theme-name">Classic Theme</div>
                                <div class="theme-description">Traditional, timeless design with elegant styling</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overlay -->
        <div class="drawer-overlay" id="drawerOverlay"></div>


        <script>
            $(document).on('click', '.switch-theme-btn', function (e) {
                e.preventDefault();

                const selectedTheme = $(this).data('theme');

                $.ajax({
                    url: '{{ route('theme.switch') }}',
                    type: 'POST',
                    data: {
                        theme: selectedTheme,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        if (response.success) {
                            location.reload(); // Reload page to apply new theme
                        }
                    },
                    error: function (xhr) {
                        alert('Failed to switch theme');
                    }
                });
            });
        </script>

        @if(($active_theme ?? 'new') === 'old')
            @include('front-end.old.navbar')
        @else
            @include('front-end.navbar')
        @endif
    </div>
    @include('labels')
    <header>

    </header>
    <main class="mt-4 ">

        @yield('content')

    </main>
    <footer>
        @if(($active_theme ?? 'new') === 'old')
            @include('front-end.old.footer')
        @else
            @include('front-end.footer')
        @endif  
    </footer>


    @include('front-end.include-js')

</body>


</html>