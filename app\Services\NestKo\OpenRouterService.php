<?php

namespace App\Services\NestKo;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\AiConversation;
use App\Models\User;

class OpenRouterService
{
    protected $apiKey;
    protected $baseUrl;
    protected $model;
    protected $rateLimitPerMinute;
    protected $cacheEnabled;
    protected $cacheTtl;

    public function __construct()
    {
        $this->apiKey = getNestkoConfig('openrouter.api_key');
        $this->baseUrl = getNestkoConfig('openrouter.base_url', 'https://openrouter.ai/api/v1');
        $this->model = getNestkoConfig('openrouter.model', 'glm4.5-air');
        $this->rateLimitPerMinute = getNestkoConfig('openrouter.rate_limit.per_minute', 60);
        $this->cacheEnabled = getNestkoConfig('openrouter.cache.enabled', true);
        $this->cacheTtl = getNestkoConfig('openrouter.cache.ttl', 3600);
    }

    /**
     * Send a chat completion request to OpenRouter
     */
    public function chatCompletion(array $messages, array $options = []): array
    {
        if (!canAccessAI()) {
            throw new \Exception('AI features are not enabled');
        }

        if (!$this->apiKey) {
            throw new \Exception('OpenRouter API key is not configured');
        }

        // Check rate limiting
        if (!$this->checkRateLimit()) {
            throw new \Exception('Rate limit exceeded. Please try again later.');
        }

        // Prepare request data
        $requestData = [
            'model' => $options['model'] ?? $this->model,
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 1.0,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $options['presence_penalty'] ?? 0.0,
        ];

        // Add optional parameters
        if (isset($options['stream'])) {
            $requestData['stream'] = $options['stream'];
        }

        try {
            $startTime = microtime(true);
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => config('app.url'),
                'X-Title' => 'NestKo AI Assistant',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $requestData);

            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000); // Convert to milliseconds

            if (!$response->successful()) {
                Log::error('OpenRouter API error', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'request' => $requestData
                ]);
                throw new \Exception('AI service error: ' . $response->body());
            }

            $result = $response->json();
            
            // Add response metadata
            $result['response_time_ms'] = $responseTime;
            $result['model_used'] = $requestData['model'];
            
            // Update rate limiting
            $this->updateRateLimit();

            return $result;

        } catch (\Exception $e) {
            Log::error('OpenRouter service error', [
                'error' => $e->getMessage(),
                'request' => $requestData
            ]);
            throw $e;
        }
    }

    /**
     * Generate AI response for project assistant
     */
    public function generateProjectAssistantResponse(
        int $userId,
        int $workspaceId,
        string $userMessage,
        array $context = []
    ): array {
        $user = User::find($userId);
        if (!$user) {
            throw new \Exception('User not found');
        }

        // Build context-aware system message
        $systemMessage = $this->buildProjectAssistantSystemMessage($user, $workspaceId, $context);
        
        // Prepare conversation history
        $messages = [
            ['role' => 'system', 'content' => $systemMessage],
            ['role' => 'user', 'content' => $userMessage]
        ];

        // Add recent conversation history for context
        $recentConversations = AiConversation::where('user_id', $userId)
            ->where('workspace_id', $workspaceId)
            ->where('context_type', 'project')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        foreach ($recentConversations->reverse() as $conversation) {
            if ($conversation->role !== 'system') {
                $messages[] = [
                    'role' => $conversation->role,
                    'content' => decrypt($conversation->encrypted_content)
                ];
            }
        }

        // Generate response
        $response = $this->chatCompletion($messages, [
            'max_tokens' => 800,
            'temperature' => 0.7
        ]);

        // Store conversation
        $this->storeConversation($userId, $workspaceId, $userMessage, $response, 'project', $context);

        return $response;
    }

    /**
     * Generate task summary using AI
     */
    public function generateTaskSummary(string $taskDescription, array $context = []): array
    {
        $systemMessage = "You are an expert at creating clear, concise task summaries. Take the provided task description and create a brief, actionable summary that captures the essential requirements and objectives. Keep it under 100 words and focus on what needs to be done, not how to do it.";

        $userMessage = "Please summarize this task:\n\n" . $taskDescription;
        
        if (!empty($context)) {
            $userMessage .= "\n\nAdditional context:\n" . json_encode($context, JSON_PRETTY_PRINT);
        }

        $messages = [
            ['role' => 'system', 'content' => $systemMessage],
            ['role' => 'user', 'content' => $userMessage]
        ];

        return $this->chatCompletion($messages, [
            'max_tokens' => 200,
            'temperature' => 0.5
        ]);
    }

    /**
     * Generate smart suggestions for tasks/projects
     */
    public function generateSmartSuggestions(string $type, array $data): array
    {
        $systemMessage = $this->buildSmartSuggestionsSystemMessage($type);
        
        $userMessage = "Please analyze this {$type} and provide smart suggestions:\n\n" . json_encode($data, JSON_PRETTY_PRINT);

        $messages = [
            ['role' => 'system', 'content' => $systemMessage],
            ['role' => 'user', 'content' => $userMessage]
        ];

        return $this->chatCompletion($messages, [
            'max_tokens' => 600,
            'temperature' => 0.6
        ]);
    }

    /**
     * Build system message for project assistant
     */
    protected function buildProjectAssistantSystemMessage(User $user, int $workspaceId, array $context): string
    {
        $systemMessage = "You are an AI project assistant for {$user->first_name} {$user->last_name} in their NestKo workspace. ";
        $systemMessage .= "You help with project management, task organization, and productivity. ";
        $systemMessage .= "Be helpful, concise, and actionable in your responses. ";
        $systemMessage .= "You can help with task creation, project planning, deadline suggestions, and team coordination. ";
        
        if (!empty($context['projects'])) {
            $systemMessage .= "\n\nCurrent projects: " . implode(', ', array_column($context['projects'], 'title'));
        }
        
        if (!empty($context['tasks'])) {
            $systemMessage .= "\n\nRecent tasks: " . implode(', ', array_column($context['tasks'], 'title'));
        }

        return $systemMessage;
    }

    /**
     * Build system message for smart suggestions
     */
    protected function buildSmartSuggestionsSystemMessage(string $type): string
    {
        switch ($type) {
            case 'task':
                return "You are an expert project manager. Analyze the task data and provide suggestions for: optimal deadline, best assignee based on skills/workload, priority level, and task dependencies. Return suggestions in JSON format with reasoning.";
            
            case 'project':
                return "You are an expert project manager. Analyze the project data and provide suggestions for: project timeline, milestone breakdown, team composition, risk assessment, and resource allocation. Return suggestions in JSON format with reasoning.";
            
            default:
                return "You are an expert productivity consultant. Analyze the provided data and give actionable suggestions for improvement.";
        }
    }

    /**
     * Store AI conversation in database
     */
    protected function storeConversation(
        int $userId,
        int $workspaceId,
        string $userMessage,
        array $aiResponse,
        string $contextType,
        array $context = []
    ): void {
        $conversationId = \Str::uuid();
        
        // Store user message
        AiConversation::create([
            'user_id' => $userId,
            'workspace_id' => $workspaceId,
            'conversation_id' => $conversationId,
            'role' => 'user',
            'encrypted_content' => encrypt($userMessage),
            'context_type' => $contextType,
            'project_id' => $context['project_id'] ?? null,
            'task_id' => $context['task_id'] ?? null,
            'ai_model' => $aiResponse['model_used'] ?? $this->model,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        // Store AI response
        if (isset($aiResponse['choices'][0]['message']['content'])) {
            AiConversation::create([
                'user_id' => $userId,
                'workspace_id' => $workspaceId,
                'conversation_id' => $conversationId,
                'role' => 'assistant',
                'encrypted_content' => encrypt($aiResponse['choices'][0]['message']['content']),
                'context_type' => $contextType,
                'project_id' => $context['project_id'] ?? null,
                'task_id' => $context['task_id'] ?? null,
                'ai_model' => $aiResponse['model_used'] ?? $this->model,
                'tokens_used' => $aiResponse['usage']['total_tokens'] ?? null,
                'cost' => $this->calculateCost($aiResponse['usage']['total_tokens'] ?? 0),
                'response_time_ms' => $aiResponse['response_time_ms'] ?? null,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }
    }

    /**
     * Check rate limiting
     */
    protected function checkRateLimit(): bool
    {
        $key = 'openrouter_rate_limit:' . request()->ip();
        $current = Cache::get($key, 0);
        
        return $current < $this->rateLimitPerMinute;
    }

    /**
     * Update rate limiting counter
     */
    protected function updateRateLimit(): void
    {
        $key = 'openrouter_rate_limit:' . request()->ip();
        $current = Cache::get($key, 0);
        Cache::put($key, $current + 1, 60); // 1 minute TTL
    }

    /**
     * Calculate estimated cost (placeholder - update with actual pricing)
     */
    protected function calculateCost(int $tokens): float
    {
        // GLM-4.5-Air is free, but we'll track for future models
        return 0.0;
    }

    /**
     * Get conversation history for a user
     */
    public function getConversationHistory(
        int $userId,
        int $workspaceId,
        string $contextType = null,
        int $limit = 50
    ) {
        $query = AiConversation::where('user_id', $userId)
            ->where('workspace_id', $workspaceId)
            ->orderBy('created_at', 'desc');

        if ($contextType) {
            $query->where('context_type', $contextType);
        }

        return $query->limit($limit)->get()->map(function ($conversation) {
            $conversation->decrypted_content = decrypt($conversation->encrypted_content);
            return $conversation;
        });
    }

    /**
     * Clear conversation history
     */
    public function clearConversationHistory(int $userId, int $workspaceId, string $contextType = null): bool
    {
        $query = AiConversation::where('user_id', $userId)
            ->where('workspace_id', $workspaceId);

        if ($contextType) {
            $query->where('context_type', $contextType);
        }

        return $query->delete() > 0;
    }
}
