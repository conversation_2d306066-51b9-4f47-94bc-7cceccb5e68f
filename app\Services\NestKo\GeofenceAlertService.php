<?php

namespace App\Services\NestKo;

use App\Models\Geofence;
use App\Models\User;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class GeofenceAlertService
{
    /**
     * Send geofence alert to configured users
     */
    public function sendGeofenceAlert(Geofence $geofence, int $userId, string $action): void
    {
        if (!canAccessGeolocation()) {
            return;
        }

        $user = User::find($userId);
        if (!$user) {
            return;
        }

        $alertMethods = $geofence->alert_methods ?? ['push'];
        $alertUsers = $this->getAlertUsers($geofence);
        
        $message = $this->buildAlertMessage($geofence, $user, $action);

        foreach ($alertUsers as $alertUser) {
            foreach ($alertMethods as $method) {
                try {
                    switch ($method) {
                        case 'push':
                            $this->sendPushNotification($alertUser, $message, $geofence, $action);
                            break;
                        case 'email':
                            $this->sendEmailAlert($alertUser, $message, $geofence, $user, $action);
                            break;
                        case 'sms':
                            $this->sendSmsAlert($alertUser, $message, $geofence, $user, $action);
                            break;
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to send geofence alert', [
                        'geofence_id' => $geofence->id,
                        'user_id' => $userId,
                        'alert_user_id' => $alertUser->id,
                        'method' => $method,
                        'action' => $action,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * Get users to alert for this geofence
     */
    protected function getAlertUsers(Geofence $geofence)
    {
        if ($geofence->alert_users && !empty($geofence->alert_users)) {
            return User::whereIn('id', $geofence->alert_users)->get();
        }

        // Default to workspace admins if no specific users configured
        return User::whereHas('workspaces', function ($query) use ($geofence) {
            $query->where('workspace_id', $geofence->workspace_id);
        })->whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();
    }

    /**
     * Build alert message
     */
    protected function buildAlertMessage(Geofence $geofence, User $user, string $action): string
    {
        if ($geofence->alert_message) {
            return str_replace(
                ['{user_name}', '{geofence_name}', '{action}'],
                [$user->first_name . ' ' . $user->last_name, $geofence->name, $action],
                $geofence->alert_message
            );
        }

        $actionText = $action === 'enter' ? 'entered' : 'exited';
        return "{$user->first_name} {$user->last_name} has {$actionText} the {$geofence->name} area.";
    }

    /**
     * Send push notification
     */
    protected function sendPushNotification(User $alertUser, string $message, Geofence $geofence, string $action): void
    {
        // Create in-app notification
        Notification::create([
            'user_id' => $alertUser->id,
            'type' => 'geofence_alert',
            'title' => 'Geofence Alert',
            'message' => $message,
            'action_url' => route('nestko.geolocation.map', ['geofence' => $geofence->id]),
            'data' => [
                'geofence_id' => $geofence->id,
                'action' => $action,
                'type' => 'geofence_alert'
            ]
        ]);

        // TODO: Implement push notification to mobile devices
        // This would integrate with FCM or similar service
    }

    /**
     * Send email alert
     */
    protected function sendEmailAlert(User $alertUser, string $message, Geofence $geofence, User $user, string $action): void
    {
        $subject = "Geofence Alert: {$geofence->name}";
        
        $emailData = [
            'alert_user' => $alertUser,
            'message' => $message,
            'geofence' => $geofence,
            'user' => $user,
            'action' => $action,
            'map_url' => route('nestko.geolocation.map', ['geofence' => $geofence->id])
        ];

        // TODO: Create email template and send
        // Mail::to($alertUser->email)->send(new GeofenceAlertMail($emailData));
        
        Log::info('Geofence email alert queued', [
            'to' => $alertUser->email,
            'geofence_id' => $geofence->id,
            'action' => $action
        ]);
    }

    /**
     * Send SMS alert
     */
    protected function sendSmsAlert(User $alertUser, string $message, Geofence $geofence, User $user, string $action): void
    {
        if (!$alertUser->phone) {
            Log::warning('Cannot send SMS alert - no phone number', [
                'user_id' => $alertUser->id,
                'geofence_id' => $geofence->id
            ]);
            return;
        }

        // TODO: Implement SMS service integration (Twilio, etc.)
        Log::info('Geofence SMS alert queued', [
            'to' => $alertUser->phone,
            'geofence_id' => $geofence->id,
            'action' => $action,
            'message' => $message
        ]);
    }

    /**
     * Test geofence alerts
     */
    public function testGeofenceAlert(Geofence $geofence, int $testUserId): array
    {
        $results = [];
        $testUser = User::find($testUserId);
        
        if (!$testUser) {
            return ['error' => 'Test user not found'];
        }

        $alertUsers = $this->getAlertUsers($geofence);
        $message = $this->buildAlertMessage($geofence, $testUser, 'enter');

        foreach ($alertUsers as $alertUser) {
            $results[] = [
                'user_id' => $alertUser->id,
                'user_name' => $alertUser->first_name . ' ' . $alertUser->last_name,
                'email' => $alertUser->email,
                'phone' => $alertUser->phone,
                'message' => $message,
                'methods' => $geofence->alert_methods ?? ['push']
            ];
        }

        return $results;
    }

    /**
     * Get geofence alert statistics
     */
    public function getAlertStatistics(int $workspaceId, int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        // Get alert counts from location history
        $enterAlerts = \App\Models\LocationHistory::where('workspace_id', $workspaceId)
            ->where('event_type', 'geofence_enter')
            ->where('recorded_at', '>=', $startDate)
            ->count();

        $exitAlerts = \App\Models\LocationHistory::where('workspace_id', $workspaceId)
            ->where('event_type', 'geofence_exit')
            ->where('recorded_at', '>=', $startDate)
            ->count();

        $totalGeofences = Geofence::where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->count();

        return [
            'period_days' => $days,
            'total_geofences' => $totalGeofences,
            'enter_alerts' => $enterAlerts,
            'exit_alerts' => $exitAlerts,
            'total_alerts' => $enterAlerts + $exitAlerts,
            'avg_alerts_per_day' => round(($enterAlerts + $exitAlerts) / $days, 2)
        ];
    }
}
