<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_suggestions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');

            // Suggestion target
            $table->enum('target_type', ['task', 'project', 'user', 'general']);
            $table->foreignId('target_id')->nullable(); // ID of the target entity

            // Suggestion details
            $table->enum('suggestion_type', ['deadline', 'assignee', 'priority', 'order', 'summary', 'optimization']);
            $table->string('title');
            $table->text('description');
            $table->json('suggestion_data'); // Structured suggestion data

            // AI generation details
            $table->string('ai_model')->default('glm4.5-air');
            $table->decimal('confidence_score', 3, 2)->nullable(); // 0.00 to 1.00
            $table->text('reasoning')->nullable(); // AI's reasoning for the suggestion
            $table->integer('tokens_used')->nullable();

            // User interaction
            $table->enum('status', ['pending', 'accepted', 'rejected', 'partially_applied'])->default('pending');
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->text('review_notes')->nullable();

            // Application tracking
            $table->boolean('is_applied')->default(false);
            $table->timestamp('applied_at')->nullable();
            $table->json('applied_changes')->nullable(); // What changes were made

            // Feedback and learning
            $table->enum('effectiveness', ['very_helpful', 'helpful', 'neutral', 'not_helpful', 'harmful'])->nullable();
            $table->text('feedback_notes')->nullable();

            // Metadata
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['workspace_id', 'status']);
            $table->index(['target_type', 'target_id']);
            $table->index(['suggestion_type', 'created_at']);
            $table->index(['generated_by', 'created_at']);
            $table->index('confidence_score');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_suggestions');
    }
};
