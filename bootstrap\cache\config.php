<?php return array (
  'app' => 
  array (
    'name' => '',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost:8000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'key' => 'base64:bzFxLeVAiEhh8GSCl91HGdCecRc0tbon4T8ZAwO++3k=',
    'cipher' => 'AES-256-CBC',
    'maintenance' => 
    array (
      'driver' => 'file',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'App\\Providers\\CustomBladeDirectivesServiceProvider',
      23 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
      24 => 'App\\Providers\\AppServiceProvider',
      25 => 'App\\Providers\\AuthServiceProvider',
      26 => 'App\\Providers\\EventServiceProvider',
      27 => 'App\\Providers\\RouteServiceProvider',
      28 => 'App\\Providers\\NestKoServiceProvider',
      29 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'token',
        'provider' => 'users',
      ),
      'client' => 
      array (
        'driver' => 'session',
        'provider' => 'clients',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
      'clients' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\Client',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 60,
        'throttle' => 60,
      ),
      'clients' => 
      array (
        'provider' => 'clients',
        'table' => 'password_resets',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '75e192515e68b',
        'secret' => '83b3bfc1e2',
        'app_id' => '1760901',
        'options' => 
        array (
          'host' => 'api-ap2.pusher.com',
          'port' => '',
          'scheme' => 'http',
          'encrypted' => true,
          'useTLS' => false,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
        'lock_connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'laravel_cache_',
  ),
  'chatify' => 
  array (
    'name' => '',
    'storage_disk_name' => 'public',
    'routes' => 
    array (
      'prefix' => 'chat',
      'middleware' => 
      array (
        0 => 'web',
        1 => 'auth',
        2 => 'has_workspace',
      ),
      'namespace' => 'App\\Http\\Controllers\\vendor\\Chatify',
    ),
    'api_routes' => 
    array (
      'prefix' => 'chatify/api',
      'middleware' => 
      array (
        0 => 'api',
      ),
      'namespace' => 'App\\Http\\Controllers\\vendor\\Chatify\\Api',
    ),
    'pusher' => 
    array (
      'debug' => true,
      'key' => '',
      'secret' => '',
      'app_id' => '',
      'options' => 
      array (
        'cluster' => '',
        'encrypted' => true,
      ),
    ),
    'user_avatar' => 
    array (
      'folder' => 'users-avatar',
      'default' => 'avatar.png',
    ),
    'gravatar' => 
    array (
      'enabled' => true,
      'image_size' => 200,
      'imageset' => 'identicon',
    ),
    'attachments' => 
    array (
      'folder' => 'attachments',
      'download_route_name' => 'attachments.download',
      'allowed_images' => 
      array (
        0 => 'png',
        1 => 'jpg',
        2 => 'jpeg',
        3 => 'gif',
      ),
      'allowed_files' => 
      array (
        0 => 'zip',
        1 => 'rar',
        2 => 'txt',
      ),
      'max_upload_size' => 150,
    ),
    'colors' => 
    array (
      0 => '#2180f3',
      1 => '#2196F3',
      2 => '#00BCD4',
      3 => '#3F51B5',
      4 => '#673AB7',
      5 => '#4CAF50',
      6 => '#FFC107',
      7 => '#FF9800',
      8 => '#ff2522',
      9 => '#9C27B0',
    ),
    'sounds' => 
    array (
      'enabled' => true,
      'public_path' => 'sounds/chatify',
      'new_message' => 'new-message-sound.mp3',
    ),
  ),
  'comments' => 
  array (
    'model' => 'App\\Models\\Comment',
    'user' => 'App\\Models\\User',
  ),
  'constants' => 
  array (
    'UPDATE_PATH' => 'update/',
    'ALLOW_MODIFICATION' => 1,
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'fresh_install_saas_final_test_1.2.3',
        'prefix' => '',
        'foreign_key_constraints' => true,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'fresh_install_saas_final_test_1.2.3',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'modes' => 
        array (
          0 => 'STRICT_TRANS_TABLES',
          1 => 'NO_ZERO_IN_DATE',
          2 => 'NO_ZERO_DATE',
          3 => 'ERROR_FOR_DIVISION_BY_ZERO',
          4 => 'NO_ENGINE_SUBSTITUTION',
        ),
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'fresh_install_saas_final_test_1.2.3',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'fresh_install_saas_final_test_1.2.3',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'laravel_database_',
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\app',
        'throw' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\app/public',
        'url' => 'http://localhost:8000/storage',
        'visibility' => 'public',
        'throw' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => '',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
      ),
    ),
    'links' => 
    array (
      'C:\\Users\\<USER>\\Downloads\\code12\\public\\storage' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\app/public',
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 10,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
    ),
  ),
  'laravelpwa' => 
  array (
    'name' => 'Laravel',
    'manifest' => 
    array (
      'name' => 'Laravel',
      'short_name' => 'Taskify Saas',
      'start_url' => '/',
      'background_color' => '#FFFFFF',
      'description' => 'Taskify SaaS is a project mangangement and task management system for handling tasks and projects. It facilitates collaboration, task allocation, scheduling, and tracking of project progress.',
      'theme_color' => '#000000',
      'display' => 'standalone',
      'orientation' => 'any',
      'status_bar' => 'black',
      'icons' => 
      array (
        0 => 
        array (
          'path' => '/storage/logos/default_favicon.png',
          'sizes' => '192x192',
          'type' => 'image/png',
          'purpose' => 'any',
        ),
        1 => 
        array (
          'src' => '/storage/logos/default_full_logo.png',
          'sizes' => '512x512',
          'type' => 'image/png',
          'purpose' => 'any',
        ),
      ),
      'custom' => 
      array (
        'screenshots' => 
        array (
        ),
      ),
    ),
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\logs/laravel.log',
        'level' => 'debug',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'host' => 'smtp.gmail.com',
        'port' => '587',
        'encryption' => 'tls',
        'username' => '',
        'password' => '',
        'timeout' => NULL,
        'local_domain' => NULL,
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
      ),
    ),
    'from' => 
    array (
      'address' => '',
      'name' => '',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\Users\\<USER>\\Downloads\\code12\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'manifest' => 
  array (
    'name' => 'Taskify SaaS',
    'short_name' => 'Taskify SaaS',
    'start_url' => '/',
    'background_color' => '#696cff',
    'description' => 'Taskify SaaS is a project mangangement and task management system for handling tasks and projects. It facilitates collaboration, task allocation, scheduling, and tracking of project progress.',
    'display' => 'fullscreen',
    'theme_color' => '#696cff',
    'icons' => 
    array (
      0 => 
      array (
        'src' => '/storage/logos/default_full_logo.png',
        'sizes' => '512x512',
        'type' => 'image/png',
        'purpose' => 'any maskable',
      ),
    ),
  ),
  'media-library' => 
  array (
    'disk_name' => 'public',
    'max_file_size' => 10485760,
    'queue_connection_name' => 'sync',
    'queue_name' => '',
    'queue_conversions_by_default' => true,
    'media_model' => 'Spatie\\MediaLibrary\\MediaCollections\\Models\\Media',
    'use_default_collection_serialization' => false,
    'temporary_upload_model' => 'Spatie\\MediaLibraryPro\\Models\\TemporaryUpload',
    'enable_temporary_uploads_session_affinity' => true,
    'generate_thumbnails_for_temporary_uploads' => true,
    'file_namer' => 'Spatie\\MediaLibrary\\Support\\FileNamer\\DefaultFileNamer',
    'path_generator' => 'App\\Services\\CustomPathGenerator',
    'file_remover_class' => 'Spatie\\MediaLibrary\\Support\\FileRemover\\FileBaseFileRemover',
    'custom_path_generators' => 
    array (
    ),
    'url_generator' => 'Spatie\\MediaLibrary\\Support\\UrlGenerator\\DefaultUrlGenerator',
    'moves_media_on_update' => false,
    'version_urls' => false,
    'image_optimizers' => 
    array (
      'Spatie\\ImageOptimizer\\Optimizers\\Jpegoptim' => 
      array (
        0 => '-m85',
        1 => '--force',
        2 => '--strip-all',
        3 => '--all-progressive',
      ),
      'Spatie\\ImageOptimizer\\Optimizers\\Pngquant' => 
      array (
        0 => '--force',
      ),
      'Spatie\\ImageOptimizer\\Optimizers\\Optipng' => 
      array (
        0 => '-i0',
        1 => '-o2',
        2 => '-quiet',
      ),
      'Spatie\\ImageOptimizer\\Optimizers\\Svgo' => 
      array (
        0 => '--disable=cleanupIDs',
      ),
      'Spatie\\ImageOptimizer\\Optimizers\\Gifsicle' => 
      array (
        0 => '-b',
        1 => '-O3',
      ),
      'Spatie\\ImageOptimizer\\Optimizers\\Cwebp' => 
      array (
        0 => '-m 6',
        1 => '-pass 10',
        2 => '-mt',
        3 => '-q 90',
      ),
      'Spatie\\ImageOptimizer\\Optimizers\\Avifenc' => 
      array (
        0 => '-a cq-level=23',
        1 => '-j all',
        2 => '--min 0',
        3 => '--max 63',
        4 => '--minalpha 0',
        5 => '--maxalpha 63',
        6 => '-a end-usage=q',
        7 => '-a tune=ssim',
      ),
    ),
    'image_generators' => 
    array (
      0 => 'Spatie\\MediaLibrary\\Conversions\\ImageGenerators\\Image',
      1 => 'Spatie\\MediaLibrary\\Conversions\\ImageGenerators\\Webp',
      2 => 'Spatie\\MediaLibrary\\Conversions\\ImageGenerators\\Avif',
      3 => 'Spatie\\MediaLibrary\\Conversions\\ImageGenerators\\Pdf',
      4 => 'Spatie\\MediaLibrary\\Conversions\\ImageGenerators\\Svg',
      5 => 'Spatie\\MediaLibrary\\Conversions\\ImageGenerators\\Video',
    ),
    'temporary_directory_path' => NULL,
    'image_driver' => 'gd',
    'ffmpeg_path' => '/usr/bin/ffmpeg',
    'ffprobe_path' => '/usr/bin/ffprobe',
    'jobs' => 
    array (
      'perform_conversions' => 'Spatie\\MediaLibrary\\Conversions\\Jobs\\PerformConversionsJob',
      'generate_responsive_images' => 'Spatie\\MediaLibrary\\ResponsiveImages\\Jobs\\GenerateResponsiveImagesJob',
    ),
    'media_downloader' => 'Spatie\\MediaLibrary\\Downloaders\\DefaultDownloader',
    'remote' => 
    array (
      'extra_headers' => 
      array (
        'CacheControl' => 'max-age=604800',
      ),
    ),
    'responsive_images' => 
    array (
      'width_calculator' => 'Spatie\\MediaLibrary\\ResponsiveImages\\WidthCalculator\\FileSizeOptimizedWidthCalculator',
      'use_tiny_placeholders' => true,
      'tiny_placeholder_generator' => 'Spatie\\MediaLibrary\\ResponsiveImages\\TinyPlaceholderGenerator\\Blurred',
    ),
    'enable_vapor_uploads' => false,
    'default_loading_attribute_value' => NULL,
    'prefix' => '',
  ),
  'nestko' => 
  array (
    'features' => 
    array (
      'geolocation' => 
      array (
        'enabled' => false,
        'name' => 'Geolocation Intelligence',
        'description' => 'Location-based task assignment, live tracking, and proximity alerts',
        'icon' => 'bx bx-map',
        'modules' => 
        array (
          'location_tracking' => 
          array (
            'enabled' => true,
            'interval' => 30000,
          ),
          'task_assignment' => 
          array (
            'enabled' => true,
            'auto_assign' => true,
          ),
          'live_map' => 
          array (
            'enabled' => true,
            'provider' => 'google',
          ),
          'proximity_alerts' => 
          array (
            'enabled' => true,
            'default_radius' => 100,
          ),
          'route_optimization' => 
          array (
            'enabled' => true,
            'ai_powered' => true,
          ),
          'location_history' => 
          array (
            'enabled' => true,
            'retention_days' => 90,
          ),
        ),
      ),
      'ai_assistant' => 
      array (
        'enabled' => false,
        'name' => 'AI Productivity Suite',
        'description' => 'AI-powered project assistance, task summarization, and smart suggestions',
        'icon' => 'bx bx-brain',
        'modules' => 
        array (
          'project_assistant' => 
          array (
            'enabled' => true,
            'per_member_chat' => true,
          ),
          'task_summarizer' => 
          array (
            'enabled' => true,
            'auto_summarize' => false,
          ),
          'smart_suggestions' => 
          array (
            'enabled' => true,
            'suggest_deadlines' => true,
            'suggest_assignee' => true,
            'suggest_order' => true,
          ),
        ),
      ),
    ),
    'openrouter' => 
    array (
      'api_key' => NULL,
      'model' => 'glm4.5-air',
      'base_url' => 'https://openrouter.ai/api/v1',
      'rate_limit' => 
      array (
        'per_minute' => 60,
        'per_hour' => 1000,
      ),
      'cache' => 
      array (
        'enabled' => true,
        'ttl' => 3600,
      ),
    ),
    'maps' => 
    array (
      'provider' => 'mapbox',
      'mapbox' => 
      array (
        'access_token' => NULL,
        'style' => 'mapbox://styles/mapbox/streets-v11',
      ),
      'google' => 
      array (
        'api_key' => NULL,
        'libraries' => 
        array (
          0 => 'places',
          1 => 'geometry',
        ),
      ),
      'default_center' => 
      array (
        'lat' => 40.7128,
        'lng' => -74.006,
      ),
      'default_zoom' => 10,
    ),
    'geolocation' => 
    array (
      'tracking' => 
      array (
        'enabled' => true,
        'interval' => 30000,
        'high_accuracy' => true,
        'timeout' => 10000,
        'maximum_age' => 60000,
      ),
      'geofencing' => 
      array (
        'default_radius' => 100,
        'max_radius' => 5000,
        'min_radius' => 10,
      ),
      'privacy' => 
      array (
        'require_consent' => true,
        'anonymize_after_days' => 30,
        'encrypt_coordinates' => true,
      ),
    ),
    'responsive' => 
    array (
      'breakpoints' => 
      array (
        'mobile' => 480,
        'tablet' => 1024,
        'desktop' => 1025,
      ),
      'map_scaling' => 
      array (
        'mobile' => 
        array (
          'height' => '300px',
          'controls' => 'minimal',
        ),
        'tablet' => 
        array (
          'height' => '400px',
          'controls' => 'standard',
        ),
        'desktop' => 
        array (
          'height' => '500px',
          'controls' => 'full',
        ),
      ),
    ),
    'request' => 
    array (
      'timeout' => 30,
      'retries' => 3,
      'user_agent' => 'NestKo/1.0',
    ),
    'security' => 
    array (
      'location_data_encryption' => true,
      'ai_conversation_encryption' => true,
      'audit_trail' => true,
      'data_retention' => 
      array (
        'location_history' => 90,
        'ai_conversations' => 365,
        'audit_logs' => 730,
      ),
    ),
  ),
  'permission' => 
  array (
    'models' => 
    array (
      'permission' => 'Spatie\\Permission\\Models\\Permission',
      'role' => 'Spatie\\Permission\\Models\\Role',
    ),
    'table_names' => 
    array (
      'roles' => 'roles',
      'permissions' => 'permissions',
      'model_has_permissions' => 'model_has_permissions',
      'model_has_roles' => 'model_has_roles',
      'role_has_permissions' => 'role_has_permissions',
    ),
    'column_names' => 
    array (
      'role_pivot_key' => NULL,
      'permission_pivot_key' => NULL,
      'model_morph_key' => 'model_id',
      'team_foreign_key' => 'team_id',
    ),
    'register_permission_check_method' => true,
    'teams' => false,
    'display_permission_in_exception' => false,
    'display_role_in_exception' => false,
    'enable_wildcard_permission' => false,
    'cache' => 
    array (
      'expiration_time' => 
      \DateInterval::__set_state(array(
         'from_string' => true,
         'date_string' => '24 hours',
      )),
      'key' => 'spatie.permission.cache',
      'store' => 'default',
    ),
  ),
  'queue' => 
  array (
    'default' => 'sync',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost:8000',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'verify_csrf_token' => 'App\\Http\\Middleware\\VerifyCsrfToken',
      'encrypt_cookies' => 'App\\Http\\Middleware\\EncryptCookies',
    ),
  ),
  'scribe' => 
  array (
    'title' => ' API Documentation',
    'description' => '',
    'base_url' => 'http://localhost:8000',
    'routes' => 
    array (
      0 => 
      array (
        'match' => 
        array (
          'prefixes' => 
          array (
            0 => 'api/*',
          ),
          'domains' => 
          array (
            0 => '*',
          ),
        ),
        'include' => 
        array (
        ),
        'exclude' => 
        array (
        ),
      ),
    ),
    'type' => 'laravel',
    'theme' => 'default',
    'static' => 
    array (
      'output_path' => 'public/docs',
    ),
    'laravel' => 
    array (
      'add_routes' => true,
      'docs_url' => '/docs',
      'assets_directory' => NULL,
      'middleware' => 
      array (
      ),
    ),
    'external' => 
    array (
      'html_attributes' => 
      array (
      ),
    ),
    'try_it_out' => 
    array (
      'enabled' => true,
      'base_url' => NULL,
      'use_csrf' => false,
      'csrf_url' => '/sanctum/csrf-cookie',
    ),
    'auth' => 
    array (
      'enabled' => false,
      'default' => false,
      'in' => 'bearer',
      'name' => 'key',
      'use_value' => NULL,
      'placeholder' => '{YOUR_AUTH_KEY}',
      'extra_info' => 'You can retrieve your token by visiting your dashboard and clicking <b>Generate API token</b>.',
    ),
    'intro_text' => '    This documentation aims to provide all the information you need to work with our API.

    <aside>As you scroll, you\'ll see code examples for working with the API in different programming languages in the dark area to the right (or as part of the content on mobile).
    You can switch the language used with the tabs at the top right (or from the nav menu at the top left on mobile).</aside>',
    'example_languages' => 
    array (
      0 => 'bash',
      1 => 'javascript',
    ),
    'postman' => 
    array (
      'enabled' => true,
      'overrides' => 
      array (
      ),
    ),
    'openapi' => 
    array (
      'enabled' => true,
      'overrides' => 
      array (
      ),
      'generators' => 
      array (
      ),
    ),
    'groups' => 
    array (
      'default' => 'Endpoints',
      'order' => 
      array (
        0 => 'Endpoints',
        'User Authentication' => 
        array (
          0 => 'POST /register',
          1 => 'POST /login',
          2 => 'POST /forgot-password',
          3 => 'POST /reset-password',
        ),
        'Dashboard' => 
        array (
          0 => 'GET /dashboardList',
          1 => 'GET /upcoming-birthdays',
          2 => 'GET /upcoming-work-anniversaries',
          3 => 'GET /members-on-leave',
        ),
        'Project Managemant' => 
        array (
          0 => 'GET /projects/{id?}',
          1 => 'POST /projects',
          2 => 'PUT /projects/{id}',
          3 => 'DELETE /projects/{id}',
          4 => 'DELETE/destroy-multiple-projects',
          5 => 'POST /update_favorite/{id}',
          6 => 'POST /projects/{id}/duplicate',
        ),
        'Project Media' => 
        array (
          0 => 'POST /projects/upload-media',
          1 => 'GET /projects/{id}/media',
          2 => 'DELETE /media/{id}',
          3 => 'POST /media/delete-multiple',
        ),
        'Project Milestones' => 
        array (
          0 => 'POST /create-milestone',
          1 => 'GET /get-milestones/{id}',
          2 => 'PUT /update-milestone/{id}',
          3 => 'DELETE /delete-milestone/{id}',
        ),
        'Project status and priority' => 
        array (
          0 => 'POST /save-view-preference',
          1 => 'PUT /update-status/{id}',
          2 => 'PUT /update-priority/{id}',
        ),
        'Project Comments' => 
        array (
          0 => 'POST /comments',
          1 => 'GET /comments/{id}',
          2 => 'PUT /comments/{id}',
          3 => 'DELETE /comments/{id}',
        ),
        'project issues' => 
        array (
          0 => 'GET /projects/issues/{id?}',
          1 => 'POST /projects/{project}/issues',
          2 => 'PUT /projects/issues/{id}',
          3 => 'DELETE /projects/issues/{id}',
        ),
        'Task Management' => 
        array (
          0 => 'GET /tasks/list-api/{id?}',
          1 => 'POST /create-tasks',
          2 => 'PUT /tasks/{id}',
          3 => 'POST /duplicate-tasks/{id}',
        ),
        'Task status and performance' => 
        array (
          0 => 'PUT /update-task-status/{id}',
        ),
        'Task Media' => 
        array (
          0 => 'POST /tasks/upload-media',
          1 => 'GET /tasks/{id}/media',
          2 => 'DELETE /media/{id}',
        ),
        'Task Celender' => 
        array (
          0 => 'GET /tasks/calendar-data',
        ),
        'Task Comments' => 
        array (
          0 => 'GET /comments/{id}',
          1 => 'PUT /comments/{id}',
          2 => 'DELETE /comments/{id}',
          3 => 'POST /comments-create  ',
        ),
        'Status Management' => 
        array (
          0 => 'GET /status/{id?}',
          1 => 'POST /statuses',
          2 => 'PUT /statuses/{id}',
          3 => 'DELETE /statuses/{id}',
          4 => 'DELETE /destroy-multiple-statuses',
        ),
        'Client Management' => 
        array (
          0 => 'GET /clients/{id?}',
          1 => 'POST /clients',
          2 => 'PUT /clients{id}',
        ),
        'Priority' => 
        array (
          0 => 'POST /priorities',
          1 => 'PUT /priorities/{id}',
          2 => 'DELETE /priorities/{id}',
          3 => 'GET /priorities/{id?}',
          4 => 'DELETE /multiple-delete',
        ),
        'User Managemant' => 
        array (
          0 => 'POST /user',
          1 => 'PUT /user/{id}',
          2 => 'DELETE /user/{id}',
          3 => 'GET /user/{id?}',
        ),
        'Workspace Managemant' => 
        array (
          0 => 'POST /workspace',
          1 => 'PUT /workspace/{id}',
          2 => 'DELETE /workspace/{id}',
          3 => 'GET /workspace/{id?}',
        ),
        'Todos Managemant' => 
        array (
          0 => 'POST /todo',
          1 => 'PUT /todo/{id}',
          2 => 'DELETE /todo/{id}',
          3 => 'GET /todo/{id?}',
          4 => 'POST /todo/update-status',
          5 => 'GET /todo/{id?}',
        ),
        'Meeting Managemant' => 
        array (
          0 => 'POST /meeting',
          1 => 'PUT /meetinf/{id}',
          2 => 'GET /meeting/{id?}',
          3 => 'DELETE /meeting/{id}',
        ),
        'note Managemant' => 
        array (
          0 => 'POST /note',
          1 => 'PUT /note/{id}',
          2 => 'DELETE /note/{id}',
          3 => 'GET /note/{id?}',
        ),
        'leaverequest Managemant ' => 
        array (
          0 => 'POST /leaverequest',
          1 => 'PUT /leaverequest/{id}',
          2 => 'DELETE /leaverequest/{id}',
          3 => 'GET /leaverequest/{id?}',
        ),
      ),
    ),
    'logo' => false,
    'last_updated' => 'Last updated: {date:F j, Y}',
    'examples' => 
    array (
      'faker_seed' => 1234,
      'models_source' => 
      array (
        0 => 'factoryCreate',
        1 => 'factoryMake',
        2 => 'databaseFirst',
      ),
    ),
    'strategies' => 
    array (
      'metadata' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Metadata\\GetFromDocBlocks',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Metadata\\GetFromMetadataAttributes',
      ),
      'headers' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Headers\\GetFromHeaderAttribute',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Headers\\GetFromHeaderTag',
        2 => 
        array (
          0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\StaticData',
          1 => 
          array (
            'only' => 
            array (
            ),
            'except' => 
            array (
            ),
            'data' => 
            array (
              'Content-Type' => 'application/json',
              'Accept' => 'application/json',
            ),
          ),
        ),
      ),
      'urlParameters' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\UrlParameters\\GetFromLaravelAPI',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\UrlParameters\\GetFromUrlParamAttribute',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\UrlParameters\\GetFromUrlParamTag',
      ),
      'queryParameters' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromFormRequest',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromInlineValidator',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromQueryParamAttribute',
        3 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromQueryParamTag',
      ),
      'bodyParameters' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromFormRequest',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromInlineValidator',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromBodyParamAttribute',
        3 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromBodyParamTag',
      ),
      'responses' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseResponseAttributes',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseTransformerTags',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags',
        3 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseResponseTag',
        4 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseResponseFileTag',
        5 => 
        array (
          0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\ResponseCalls',
          1 => 
          array (
            'only' => 
            array (
              0 => 'GET *',
            ),
            'except' => 
            array (
            ),
            'config' => 
            array (
              'app.debug' => false,
            ),
            'queryParams' => 
            array (
            ),
            'bodyParams' => 
            array (
            ),
            'fileParams' => 
            array (
            ),
            'cookies' => 
            array (
            ),
          ),
        ),
      ),
      'responseFields' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\ResponseFields\\GetFromResponseFieldAttribute',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\ResponseFields\\GetFromResponseFieldTag',
      ),
    ),
    'database_connections_to_transact' => 
    array (
      0 => 'mysql',
    ),
    'fractal' => 
    array (
      'serializer' => NULL,
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
      'scheme' => 'https',
    ),
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '120',
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'laravel_session',
    'path' => '/',
    'domain' => NULL,
    'http_only' => true,
    'secure' => true,
    'same_site' => 'none',
  ),
  'taskify' => 
  array (
    'project_status_labels' => 
    array (
      'completed' => 'success',
      'onhold' => 'warning',
      'ongoing' => 'info',
      'started' => 'primary',
      'cancelled' => 'danger',
    ),
    'task_status_labels' => 
    array (
      'completed' => 'success',
      'onhold' => 'warning',
      'started' => 'primary',
      'cancelled' => 'danger',
      'ongoing' => 'info',
    ),
    'role_labels' => 
    array (
      'admin' => 'info',
      'Super Admin' => 'danger',
      'HR' => 'primary',
      'member' => 'warning',
      'default' => 'dark',
    ),
    'priority_labels' => 
    array (
      'low' => 'success',
      'High' => 'danger',
      'medium' => 'warning',
    ),
    'permissions' => 
    array (
      'Projects' => 
      array (
        0 => 'create_projects',
        1 => 'manage_projects',
        2 => 'edit_projects',
        3 => 'delete_projects',
      ),
      'Tasks' => 
      array (
        0 => 'create_tasks',
        1 => 'manage_tasks',
        2 => 'edit_tasks',
        3 => 'delete_tasks',
      ),
      'Statuses' => 
      array (
        0 => 'create_statuses',
        1 => 'manage_statuses',
        2 => 'edit_statuses',
        3 => 'delete_statuses',
      ),
      'Priorities' => 
      array (
        0 => 'create_priorities',
        1 => 'manage_priorities',
        2 => 'edit_priorities',
        3 => 'delete_priorities',
      ),
      'Tags' => 
      array (
        0 => 'create_tags',
        1 => 'manage_tags',
        2 => 'edit_tags',
        3 => 'delete_tags',
      ),
      'Users' => 
      array (
        0 => 'create_users',
        1 => 'manage_users',
        2 => 'edit_users',
        3 => 'delete_users',
      ),
      'Clients' => 
      array (
        0 => 'create_clients',
        1 => 'manage_clients',
        2 => 'edit_clients',
        3 => 'delete_clients',
      ),
      'Workspaces' => 
      array (
        0 => 'create_workspaces',
        1 => 'manage_workspaces',
        2 => 'edit_workspaces',
        3 => 'delete_workspaces',
      ),
      'Meetings' => 
      array (
        0 => 'create_meetings',
        1 => 'manage_meetings',
        2 => 'edit_meetings',
        3 => 'delete_meetings',
      ),
      'Contracts' => 
      array (
        0 => 'create_contracts',
        1 => 'manage_contracts',
        2 => 'edit_contracts',
        3 => 'delete_contracts',
      ),
      'Contract_types' => 
      array (
        0 => 'create_contract_types',
        1 => 'manage_contract_types',
        2 => 'edit_contract_types',
        3 => 'delete_contract_types',
      ),
      'Timesheet' => 
      array (
        0 => 'create_timesheet',
        1 => 'manage_timesheet',
        2 => 'delete_timesheet',
      ),
      'Media' => 
      array (
        0 => 'create_media',
        1 => 'manage_media',
        2 => 'delete_media',
      ),
      'Payslips' => 
      array (
        0 => 'create_payslips',
        1 => 'manage_payslips',
        2 => 'edit_payslips',
        3 => 'delete_payslips',
      ),
      'Allowances' => 
      array (
        0 => 'create_allowances',
        1 => 'manage_allowances',
        2 => 'edit_allowances',
        3 => 'delete_allowances',
      ),
      'Deductions' => 
      array (
        0 => 'create_deductions',
        1 => 'manage_deductions',
        2 => 'edit_deductions',
        3 => 'delete_deductions',
      ),
      'Payment methods' => 
      array (
        0 => 'create_payment_methods',
        1 => 'manage_payment_methods',
        2 => 'edit_payment_methods',
        3 => 'delete_payment_methods',
      ),
      'Activity Log' => 
      array (
        0 => 'manage_activity_log',
        1 => 'delete_activity_log',
      ),
      'Estimates Invoices' => 
      array (
        0 => 'create_estimates_invoices',
        1 => 'manage_estimates_invoices',
        2 => 'edit_estimates_invoices',
        3 => 'delete_estimates_invoices',
      ),
      'Payments' => 
      array (
        0 => 'create_payments',
        1 => 'manage_payments',
        2 => 'edit_payments',
        3 => 'delete_payments',
      ),
      'Taxes' => 
      array (
        0 => 'create_taxes',
        1 => 'manage_taxes',
        2 => 'edit_taxes',
        3 => 'delete_taxes',
      ),
      'Units' => 
      array (
        0 => 'create_units',
        1 => 'manage_units',
        2 => 'edit_units',
        3 => 'delete_units',
      ),
      'Items' => 
      array (
        0 => 'create_items',
        1 => 'manage_items',
        2 => 'edit_items',
        3 => 'delete_items',
      ),
      'Expenses' => 
      array (
        0 => 'create_expenses',
        1 => 'manage_expenses',
        2 => 'edit_expenses',
        3 => 'delete_expenses',
      ),
      'Expense types' => 
      array (
        0 => 'create_expense_types',
        1 => 'manage_expense_types',
        2 => 'edit_expense_types',
        3 => 'delete_expense_types',
      ),
      'Milestones' => 
      array (
        0 => 'create_milestones',
        1 => 'manage_milestones',
        2 => 'edit_milestones',
        3 => 'delete_milestones',
      ),
      'System Notifications' => 
      array (
        0 => 'manage_system_notifications',
        1 => 'delete_system_notifications',
      ),
      'Announcements' => 
      array (
        0 => 'create_announcements',
        1 => 'manage_announcements',
        2 => 'edit_announcements',
        3 => 'delete_announcements',
      ),
    ),
    'modules' => 
    array (
      'tasks' => 
      array (
        'icon' => 'bx bx-task',
        'description' => 'Manage tasks and assignments efficiently',
      ),
      'notes' => 
      array (
        'icon' => 'bx bx-note',
        'description' => 'Take and organize notes for better productivity',
      ),
      'meetings' => 
      array (
        'icon' => 'bx bx-calendar-event',
        'description' => 'Schedule and organize meetings with team members',
      ),
      'chat' => 
      array (
        'icon' => 'bx bx-chat',
        'description' => 'Communicate with team members in real-time',
      ),
      'todos' => 
      array (
        'icon' => 'bx bx-list-ul',
        'description' => 'Create and manage to-do lists for tasks and projects',
      ),
      'contracts' => 
      array (
        'icon' => 'bx bx-news',
        'description' => 'Manage contracts and agreements with clients',
      ),
      'payslips' => 
      array (
        'icon' => 'bx bx-box',
        'description' => 'View and manage payslips for employees',
      ),
      'finance' => 
      array (
        'icon' => 'bx bx-dollar',
        'description' => 'Create and Manange Expenses Payments and Invoice Estimates',
      ),
    ),
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\Users\\<USER>\\Downloads\\code12\\resources\\views',
    ),
    'compiled' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\framework\\views',
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => NULL,
    'convert_entities' => true,
    'options' => 
    array (
      'font_dir' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\fonts',
      'font_cache' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'C:\\Users\\<USER>\\Downloads\\code12',
      'allowed_protocols' => 
      array (
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
  ),
  'image' => 
  array (
    'driver' => 'gd',
  ),
  'scout' => 
  array (
    'driver' => 'algolia',
    'prefix' => '',
    'queue' => false,
    'after_commit' => false,
    'chunk' => 
    array (
      'searchable' => 500,
      'unsearchable' => 500,
    ),
    'soft_delete' => false,
    'identify' => false,
    'algolia' => 
    array (
      'id' => '',
      'secret' => '',
      'index-settings' => 
      array (
      ),
    ),
    'meilisearch' => 
    array (
      'host' => 'http://localhost:7700',
      'key' => NULL,
      'index-settings' => 
      array (
      ),
    ),
    'typesense' => 
    array (
      'client-settings' => 
      array (
        'api_key' => 'xyz',
        'nodes' => 
        array (
          0 => 
          array (
            'host' => 'localhost',
            'port' => '8108',
            'path' => '',
            'protocol' => 'http',
          ),
        ),
        'nearest_node' => 
        array (
          'host' => 'localhost',
          'port' => '8108',
          'path' => '',
          'protocol' => 'http',
        ),
        'connection_timeout_seconds' => 2,
        'healthcheck_interval_seconds' => 30,
        'num_retries' => 3,
        'retry_interval_seconds' => 1,
      ),
      'model-settings' => 
      array (
      ),
    ),
  ),
  'invoices' => 
  array (
    'date' => 
    array (
      'format' => 'Y-m-d',
      'pay_until_days' => 7,
    ),
    'serial_number' => 
    array (
      'series' => 'AA',
      'sequence' => 1,
      'sequence_padding' => 5,
      'delimiter' => '.',
      'format' => '{SERIES}{DELIMITER}{SEQUENCE}',
    ),
    'currency' => 
    array (
      'code' => 'eur',
      'fraction' => 'ct.',
      'symbol' => '€',
      'decimals' => 2,
      'decimal_point' => '.',
      'thousands_separator' => '',
      'format' => '{VALUE} {SYMBOL}',
    ),
    'paper' => 
    array (
      'size' => 'a4',
      'orientation' => 'portrait',
    ),
    'disk' => 'local',
    'seller' => 
    array (
      'class' => 'LaravelDaily\\Invoices\\Classes\\Seller',
      'attributes' => 
      array (
        'name' => 'Towne, Smith and Ebert',
        'address' => '89982 Pfeffer Falls Damianstad, CO 66972-8160',
        'code' => '41-1985581',
        'vat' => '*********',
        'phone' => '************',
        'custom_fields' => 
        array (
          'SWIFT' => 'BANK101',
        ),
      ),
    ),
    'dompdf_options' => 
    array (
      'enable_php' => true,
      'logOutputFile' => '/dev/null',
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
        'test_auto_detect' => true,
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => false,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'guess',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
      'cells' => 
      array (
        'middleware' => 
        array (
        ),
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\Users\\<USER>\\Downloads\\code12\\storage\\framework/cache/laravel-excel',
      'local_permissions' => 
      array (
      ),
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => NULL,
    ),
  ),
  'flare' => 
  array (
    'key' => NULL,
    'flare_middleware' => 
    array (
      0 => 'Spatie\\FlareClient\\FlareMiddleware\\RemoveRequestIp',
      1 => 'Spatie\\FlareClient\\FlareMiddleware\\AddGitInformation',
      2 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddNotifierName',
      3 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddEnvironmentInformation',
      4 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionInformation',
      5 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddDumps',
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs' => 
      array (
        'maximum_number_of_collected_logs' => 200,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries' => 
      array (
        'maximum_number_of_collected_queries' => 200,
        'report_query_bindings' => true,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs' => 
      array (
        'max_chained_job_reporting_depth' => 5,
      ),
      6 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddContext',
      7 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionHandledStatus',
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields' => 
      array (
        'censor_fields' => 
        array (
          0 => 'password',
          1 => 'password_confirmation',
        ),
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders' => 
      array (
        'headers' => 
        array (
          0 => 'API-KEY',
          1 => 'Authorization',
          2 => 'Cookie',
          3 => 'Set-Cookie',
          4 => 'X-CSRF-TOKEN',
          5 => 'X-XSRF-TOKEN',
        ),
      ),
    ),
    'send_logs_as_events' => true,
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'auto',
    'enable_share_button' => true,
    'register_commands' => false,
    'solution_providers' => 
    array (
      0 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\BadMethodCallSolutionProvider',
      1 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider',
      2 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\UndefinedPropertySolutionProvider',
      3 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\IncorrectValetDbCredentialsSolutionProvider',
      4 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingAppKeySolutionProvider',
      5 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\DefaultDbNameSolutionProvider',
      6 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\TableNotFoundSolutionProvider',
      7 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingImportSolutionProvider',
      8 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\InvalidRouteActionSolutionProvider',
      9 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\ViewNotFoundSolutionProvider',
      10 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\RunningLaravelDuskInProductionProvider',
      11 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingColumnSolutionProvider',
      12 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownValidationSolutionProvider',
      13 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingMixManifestSolutionProvider',
      14 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingViteManifestSolutionProvider',
      15 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingLivewireComponentSolutionProvider',
      16 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UndefinedViewVariableSolutionProvider',
      17 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\GenericLaravelExceptionSolutionProvider',
      18 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\OpenAiSolutionProvider',
      19 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SailNetworkSolutionProvider',
      20 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMysql8CollationSolutionProvider',
      21 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMariadbCollationSolutionProvider',
    ),
    'ignored_solution_providers' => 
    array (
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => 'C:\\Users\\<USER>\\Downloads\\code12',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
    'settings_file_path' => '',
    'recorders' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder',
      1 => 'Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder',
      2 => 'Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder',
      3 => 'Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder',
    ),
    'open_ai_key' => NULL,
    'with_stack_frame_arguments' => true,
    'argument_reducers' => 
    array (
      0 => 'Spatie\\Backtrace\\Arguments\\Reducers\\BaseTypeArgumentReducer',
      1 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ArrayArgumentReducer',
      2 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StdClassArgumentReducer',
      3 => 'Spatie\\Backtrace\\Arguments\\Reducers\\EnumArgumentReducer',
      4 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ClosureArgumentReducer',
      5 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeArgumentReducer',
      6 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeZoneArgumentReducer',
      7 => 'Spatie\\Backtrace\\Arguments\\Reducers\\SymphonyRequestArgumentReducer',
      8 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\ModelArgumentReducer',
      9 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\CollectionArgumentReducer',
      10 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StringableArgumentReducer',
    ),
  ),
  'paypal' => 
  array (
    'mode' => 'sandbox',
    'sandbox' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'app_id' => 'APP-80W284485P519543T',
    ),
    'live' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'app_id' => '',
    ),
    'payment_action' => 'Sale',
    'currency' => 'USD',
    'notify_url' => '',
    'locale' => 'en_US',
    'validate_ssl' => true,
  ),
  'captcha' => 
  array (
    'secret' => NULL,
    'sitekey' => NULL,
    'options' => 
    array (
      'timeout' => 30,
    ),
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
