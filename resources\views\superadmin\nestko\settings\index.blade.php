@extends('layout')

@section('title')
    {{ get_label('nestko_global_settings', 'NestKo Global Settings') }}
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="fw-bold py-3 mb-4">
                <span class="text-muted fw-light">{{ get_label('super_admin', 'Super Admin') }} /</span>
                {{ get_label('nestko_global_settings', 'NestKo Global Settings') }}
            </h4>
        </div>
        <div>
            <button type="button" class="btn btn-outline-primary" id="testAllApisBtn">
                <i class="bx bx-test-tube"></i> {{ get_label('test_all_apis', 'Test All APIs') }}
            </button>
            <button type="button" class="btn btn-primary" id="saveSettingsBtn">
                <i class="bx bx-save"></i> {{ get_label('save_settings', 'Save Settings') }}
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Settings Panel -->
        <div class="col-lg-8">
            <form id="globalSettingsForm">
                @csrf
                
                <!-- Geolocation Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bx bx-map me-2"></i>
                            {{ get_label('geolocation_settings', 'Geolocation Intelligence Settings') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="geoEnabled" 
                                           name="geolocation[enabled]" {{ $settings['geolocation']['enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="geoEnabled">
                                        {{ get_label('enable_geolocation', 'Enable Geolocation Features') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="trackingInterval" class="form-label">{{ get_label('default_tracking_interval', 'Default Tracking Interval (ms)') }}</label>
                                    <input type="number" class="form-control" id="trackingInterval" 
                                           name="geolocation[default_tracking_interval]" 
                                           value="{{ $settings['geolocation']['default_tracking_interval'] }}"
                                           min="5000" max="300000" step="1000">
                                    <div class="form-text">{{ get_label('tracking_interval_help', 'Minimum 5 seconds, Maximum 5 minutes') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="geofenceRadius" class="form-label">{{ get_label('default_geofence_radius', 'Default Geofence Radius (meters)') }}</label>
                                    <input type="number" class="form-control" id="geofenceRadius" 
                                           name="geolocation[default_geofence_radius]" 
                                           value="{{ $settings['geolocation']['default_geofence_radius'] }}"
                                           min="10" max="10000">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="locationHistoryDays" class="form-label">{{ get_label('max_location_history_days', 'Max Location History (days)') }}</label>
                                    <input type="number" class="form-control" id="locationHistoryDays" 
                                           name="geolocation[max_location_history_days]" 
                                           value="{{ $settings['geolocation']['max_location_history_days'] }}"
                                           min="1" max="365">
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="locationEncryption" 
                                           name="geolocation[enable_location_encryption]" {{ $settings['geolocation']['enable_location_encryption'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="locationEncryption">
                                        {{ get_label('enable_location_encryption', 'Enable Location Data Encryption') }}
                                    </label>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="allowWorkspaceGeoApi" 
                                           name="geolocation[allow_workspace_api_config]" {{ $settings['geolocation']['allow_workspace_api_config'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="allowWorkspaceGeoApi">
                                        {{ get_label('allow_workspace_api_config', 'Allow Workspace API Configuration') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Assistant Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bx bx-brain me-2"></i>
                            {{ get_label('ai_assistant_settings', 'AI Assistant Settings') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="aiEnabled" 
                                           name="ai_assistant[enabled]" {{ $settings['ai_assistant']['enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="aiEnabled">
                                        {{ get_label('enable_ai_assistant', 'Enable AI Assistant Features') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="openrouterApiKey" class="form-label">{{ get_label('openrouter_api_key', 'OpenRouter API Key') }}</label>
                                    <input type="password" class="form-control" id="openrouterApiKey" 
                                           name="ai_assistant[openrouter_api_key]" 
                                           value="{{ $settings['ai_assistant']['openrouter_api_key'] }}"
                                           placeholder="sk-or-v1-...">
                                    <div class="form-text">{{ get_label('api_key_help', 'Leave unchanged to keep current key') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="defaultModel" class="form-label">{{ get_label('default_ai_model', 'Default AI Model') }}</label>
                                    <select class="form-select" id="defaultModel" name="ai_assistant[default_model]">
                                        <option value="glm4.5-air" {{ $settings['ai_assistant']['default_model'] === 'glm4.5-air' ? 'selected' : '' }}>GLM-4.5-Air (Recommended)</option>
                                        <option value="gpt-3.5-turbo" {{ $settings['ai_assistant']['default_model'] === 'gpt-3.5-turbo' ? 'selected' : '' }}>GPT-3.5 Turbo</option>
                                        <option value="gpt-4" {{ $settings['ai_assistant']['default_model'] === 'gpt-4' ? 'selected' : '' }}>GPT-4</option>
                                        <option value="claude-3-haiku" {{ $settings['ai_assistant']['default_model'] === 'claude-3-haiku' ? 'selected' : '' }}>Claude 3 Haiku</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="globalRateLimit" class="form-label">{{ get_label('global_rate_limit', 'Global Rate Limit (requests/minute)') }}</label>
                                    <input type="number" class="form-control" id="globalRateLimit" 
                                           name="ai_assistant[global_rate_limit]" 
                                           value="{{ $settings['ai_assistant']['global_rate_limit'] }}"
                                           min="1" max="1000">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="cacheTtl" class="form-label">{{ get_label('cache_ttl', 'Cache TTL (seconds)') }}</label>
                                    <input type="number" class="form-control" id="cacheTtl" 
                                           name="ai_assistant[cache_ttl]" 
                                           value="{{ $settings['ai_assistant']['cache_ttl'] }}"
                                           min="300" max="86400">
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="aiEncryption" 
                                           name="ai_assistant[enable_conversation_encryption]" {{ $settings['ai_assistant']['enable_conversation_encryption'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="aiEncryption">
                                        {{ get_label('enable_conversation_encryption', 'Enable Conversation Encryption') }}
                                    </label>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="allowWorkspaceAiConfig" 
                                           name="ai_assistant[allow_workspace_customization]" {{ $settings['ai_assistant']['allow_workspace_customization'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="allowWorkspaceAiConfig">
                                        {{ get_label('allow_workspace_customization', 'Allow Workspace Customization') }}
                                    </label>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="costTracking" 
                                           name="ai_assistant[cost_tracking_enabled]" {{ $settings['ai_assistant']['cost_tracking_enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="costTracking">
                                        {{ get_label('enable_cost_tracking', 'Enable Cost Tracking') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- General Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bx bx-cog me-2"></i>
                            {{ get_label('general_settings', 'General Settings') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableAnalytics" 
                                           name="general[enable_analytics]" {{ $settings['general']['enable_analytics'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enableAnalytics">
                                        {{ get_label('enable_analytics', 'Enable Analytics') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="dataRetentionDays" class="form-label">{{ get_label('data_retention_days', 'Data Retention (days)') }}</label>
                                    <input type="number" class="form-control" id="dataRetentionDays" 
                                           name="general[data_retention_days]" 
                                           value="{{ $settings['general']['data_retention_days'] }}"
                                           min="30" max="1095">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableAuditLogs" 
                                           name="general[enable_audit_logs]" {{ $settings['general']['enable_audit_logs'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enableAuditLogs">
                                        {{ get_label('enable_audit_logs', 'Enable Audit Logs') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="maxWorkspacesPerFeature" class="form-label">{{ get_label('max_workspaces_per_feature', 'Max Workspaces per Feature') }}</label>
                                    <input type="number" class="form-control" id="maxWorkspacesPerFeature" 
                                           name="general[max_workspaces_per_feature]" 
                                           value="{{ $settings['general']['max_workspaces_per_feature'] }}"
                                           min="0">
                                    <div class="form-text">{{ get_label('zero_unlimited', '0 = Unlimited') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Statistics Panel -->
        <div class="col-lg-4">
            <!-- Global Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">{{ get_label('global_statistics', 'Global Statistics') }}</h6>
                </div>
                <div class="card-body">
                    @if(isset($statistics['error']))
                        <div class="alert alert-warning">{{ $statistics['error'] }}</div>
                    @else
                        <!-- Geolocation Stats -->
                        <div class="mb-3">
                            <h6 class="text-primary">{{ get_label('geolocation', 'Geolocation') }}</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ number_format($statistics['geolocation']['total_locations_tracked'] ?? 0) }}</div>
                                        <small class="text-muted">{{ get_label('total_locations', 'Total Locations') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ $statistics['geolocation']['active_workspaces'] ?? 0 }}</div>
                                        <small class="text-muted">{{ get_label('active_workspaces', 'Active Workspaces') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Stats -->
                        <div class="mb-3">
                            <h6 class="text-success">{{ get_label('ai_assistant', 'AI Assistant') }}</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ number_format($statistics['ai_assistant']['total_conversations'] ?? 0) }}</div>
                                        <small class="text-muted">{{ get_label('conversations', 'Conversations') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ number_format($statistics['ai_assistant']['total_tokens_used'] ?? 0) }}</div>
                                        <small class="text-muted">{{ get_label('tokens_used', 'Tokens Used') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- General Stats -->
                        <div>
                            <h6 class="text-info">{{ get_label('general', 'General') }}</h6>
                            <div class="text-center">
                                <div class="border rounded p-2 mb-2">
                                    <div class="fw-bold">{{ $statistics['general']['total_workspaces_using_features'] ?? 0 }}</div>
                                    <small class="text-muted">{{ get_label('workspaces_using_features', 'Workspaces Using Features') }}</small>
                                </div>
                                <div class="border rounded p-2">
                                    <div class="fw-bold">{{ $statistics['general']['data_storage_size'] ?? 'N/A' }}</div>
                                    <small class="text-muted">{{ get_label('data_storage_size', 'Data Storage Size') }}</small>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- API Status -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">{{ get_label('api_status', 'API Status') }}</h6>
                </div>
                <div class="card-body">
                    <div id="apiStatusContainer">
                        <p class="text-muted text-center">{{ get_label('click_test_apis', 'Click "Test All APIs" to check status') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Save settings
    document.getElementById('saveSettingsBtn').addEventListener('click', function() {
        const form = document.getElementById('globalSettingsForm');
        const formData = new FormData(form);
        
        // Convert FormData to JSON
        const data = {};
        for (let [key, value] of formData.entries()) {
            const keys = key.split('[').map(k => k.replace(']', ''));
            let current = data;
            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) current[keys[i]] = {};
                current = current[keys[i]];
            }
            current[keys[keys.length - 1]] = value === 'on' ? true : value;
        }
        
        fetch('{{ route('superadmin.nestko.settings.update') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success('{{ get_label('settings_updated', 'Settings updated successfully') }}');
            } else {
                toastr.error(data.error || '{{ get_label('update_failed', 'Failed to update settings') }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ get_label('update_failed', 'Failed to update settings') }}');
        });
    });

    // Test APIs
    document.getElementById('testAllApisBtn').addEventListener('click', function() {
        const apiKey = document.getElementById('openrouterApiKey').value;
        
        fetch('{{ route('superadmin.nestko.test-apis') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                test_openrouter: true,
                test_mapbox: true,
                openrouter_api_key: apiKey
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayApiStatus(data.test_results);
            } else {
                toastr.error(data.error || '{{ get_label('api_test_failed', 'API test failed') }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ get_label('api_test_failed', 'API test failed') }}');
        });
    });

    function displayApiStatus(results) {
        const container = document.getElementById('apiStatusContainer');
        let html = '';
        
        for (const [api, result] of Object.entries(results)) {
            const statusClass = result.status === 'success' ? 'success' : 'danger';
            const icon = result.status === 'success' ? 'bx-check-circle' : 'bx-x-circle';
            
            html += `
                <div class="d-flex align-items-center mb-2">
                    <i class="bx ${icon} text-${statusClass} me-2"></i>
                    <div class="flex-grow-1">
                        <div class="fw-semibold">${api.charAt(0).toUpperCase() + api.slice(1)}</div>
                        <small class="text-muted">${result.message}</small>
                        ${result.response_time ? `<br><small class="text-muted">Response: ${result.response_time}</small>` : ''}
                    </div>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
});
</script>
@endsection
