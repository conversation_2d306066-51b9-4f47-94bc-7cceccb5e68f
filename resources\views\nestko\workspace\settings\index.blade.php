@extends('layout')

@section('title')
    {{ get_label('nestko_workspace_settings', 'NestKo Workspace Settings') }}
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="fw-bold py-3 mb-4">
                <span class="text-muted fw-light">{{ get_label('workspace', 'Workspace') }} /</span>
                {{ get_label('nestko_settings', 'NestKo Settings') }}
            </h4>
        </div>
        <div>
            <button type="button" class="btn btn-outline-primary" id="testApiBtn">
                <i class="bx bx-test-tube"></i> {{ get_label('test_api', 'Test API') }}
            </button>
            <button type="button" class="btn btn-primary" id="saveWorkspaceSettingsBtn">
                <i class="bx bx-save"></i> {{ get_label('save_settings', 'Save Settings') }}
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Settings Panel -->
        <div class="col-lg-8">
            <form id="workspaceSettingsForm">
                @csrf
                
                <!-- Geolocation Settings -->
                @if(\App\Services\NestKo\RoleBasedAccessService::canAccessFeature('geolocation'))
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bx bx-map me-2"></i>
                            {{ get_label('geolocation_settings', 'Geolocation Settings') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="geoEnabled" 
                                           name="geolocation[enabled]" {{ $settings['geolocation']['enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="geoEnabled">
                                        {{ get_label('enable_geolocation_workspace', 'Enable Geolocation for this Workspace') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="trackingInterval" class="form-label">{{ get_label('tracking_interval', 'Tracking Interval (ms)') }}</label>
                                    <input type="number" class="form-control" id="trackingInterval" 
                                           name="geolocation[tracking_interval]" 
                                           value="{{ $settings['geolocation']['tracking_interval'] }}"
                                           min="5000" max="300000" step="1000">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="geofenceRadius" class="form-label">{{ get_label('geofence_radius', 'Default Geofence Radius (meters)') }}</label>
                                    <input type="number" class="form-control" id="geofenceRadius" 
                                           name="geolocation[geofence_radius]" 
                                           value="{{ $settings['geolocation']['geofence_radius'] }}"
                                           min="10" max="10000">
                                </div>
                                
                                @if($permissions['can_configure_geolocation_api'])
                                <div class="mb-3">
                                    <label for="mapboxToken" class="form-label">{{ get_label('mapbox_access_token', 'Mapbox Access Token') }}</label>
                                    <input type="password" class="form-control" id="mapboxToken" 
                                           name="geolocation[mapbox_access_token]" 
                                           value="{{ $settings['geolocation']['mapbox_access_token'] }}"
                                           placeholder="pk.eyJ1...">
                                    <div class="form-text">{{ get_label('mapbox_token_help', 'Optional: Use custom Mapbox token for this workspace') }}</div>
                                </div>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mapStyle" class="form-label">{{ get_label('map_style', 'Map Style') }}</label>
                                    <select class="form-select" id="mapStyle" name="geolocation[map_style]">
                                        <option value="mapbox://styles/mapbox/streets-v11" {{ $settings['geolocation']['map_style'] === 'mapbox://styles/mapbox/streets-v11' ? 'selected' : '' }}>Streets</option>
                                        <option value="mapbox://styles/mapbox/satellite-v9" {{ $settings['geolocation']['map_style'] === 'mapbox://styles/mapbox/satellite-v9' ? 'selected' : '' }}>Satellite</option>
                                        <option value="mapbox://styles/mapbox/satellite-streets-v11" {{ $settings['geolocation']['map_style'] === 'mapbox://styles/mapbox/satellite-streets-v11' ? 'selected' : '' }}>Hybrid</option>
                                        <option value="mapbox://styles/mapbox/outdoors-v11" {{ $settings['geolocation']['map_style'] === 'mapbox://styles/mapbox/outdoors-v11' ? 'selected' : '' }}>Outdoors</option>
                                    </select>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableAlerts" 
                                           name="geolocation[enable_alerts]" {{ $settings['geolocation']['enable_alerts'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enableAlerts">
                                        {{ get_label('enable_geofence_alerts', 'Enable Geofence Alerts') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="alertEmail" class="form-label">{{ get_label('alert_email', 'Alert Email') }}</label>
                                    <input type="email" class="form-control" id="alertEmail" 
                                           name="geolocation[alert_email]" 
                                           value="{{ $settings['geolocation']['alert_email'] }}"
                                           placeholder="<EMAIL>">
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="privacyMode" 
                                           name="geolocation[privacy_mode]" {{ $settings['geolocation']['privacy_mode'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="privacyMode">
                                        {{ get_label('privacy_mode', 'Privacy Mode (Hide exact locations)') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- AI Assistant Settings -->
                @if(\App\Services\NestKo\RoleBasedAccessService::canAccessFeature('ai_assistant'))
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bx bx-brain me-2"></i>
                            {{ get_label('ai_assistant_settings', 'AI Assistant Settings') }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="aiEnabled" 
                                           name="ai_assistant[enabled]" {{ $settings['ai_assistant']['enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="aiEnabled">
                                        {{ get_label('enable_ai_workspace', 'Enable AI Assistant for this Workspace') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="customModel" class="form-label">{{ get_label('ai_model', 'AI Model') }}</label>
                                    <select class="form-select" id="customModel" name="ai_assistant[custom_model]">
                                        <option value="glm4.5-air" {{ $settings['ai_assistant']['custom_model'] === 'glm4.5-air' ? 'selected' : '' }}>GLM-4.5-Air (Recommended)</option>
                                        <option value="gpt-3.5-turbo" {{ $settings['ai_assistant']['custom_model'] === 'gpt-3.5-turbo' ? 'selected' : '' }}>GPT-3.5 Turbo</option>
                                        <option value="gpt-4" {{ $settings['ai_assistant']['custom_model'] === 'gpt-4' ? 'selected' : '' }}>GPT-4</option>
                                        <option value="claude-3-haiku" {{ $settings['ai_assistant']['custom_model'] === 'claude-3-haiku' ? 'selected' : '' }}>Claude 3 Haiku</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="rateLimit" class="form-label">{{ get_label('rate_limit', 'Rate Limit (requests/minute)') }}</label>
                                    <input type="number" class="form-control" id="rateLimit" 
                                           name="ai_assistant[rate_limit]" 
                                           value="{{ $settings['ai_assistant']['rate_limit'] }}"
                                           min="1" max="200">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableVoiceInput" 
                                           name="ai_assistant[enable_voice_input]" {{ $settings['ai_assistant']['enable_voice_input'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enableVoiceInput">
                                        {{ get_label('enable_voice_input', 'Enable Voice Input') }}
                                    </label>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableSuggestions" 
                                           name="ai_assistant[enable_suggestions]" {{ $settings['ai_assistant']['enable_suggestions'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enableSuggestions">
                                        {{ get_label('enable_smart_suggestions', 'Enable Smart Suggestions') }}
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="conversationRetention" class="form-label">{{ get_label('conversation_retention', 'Conversation Retention (days)') }}</label>
                                    <input type="number" class="form-control" id="conversationRetention" 
                                           name="ai_assistant[conversation_retention_days]" 
                                           value="{{ $settings['ai_assistant']['conversation_retention_days'] }}"
                                           min="1" max="365">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </form>
        </div>

        <!-- Statistics Panel -->
        <div class="col-lg-4">
            <!-- Workspace Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">{{ get_label('workspace_statistics', 'Workspace Statistics') }}</h6>
                </div>
                <div class="card-body">
                    @if(isset($statistics['error']))
                        <div class="alert alert-warning">{{ $statistics['error'] }}</div>
                    @else
                        @if(\App\Services\NestKo\RoleBasedAccessService::canAccessFeature('geolocation'))
                        <!-- Geolocation Stats -->
                        <div class="mb-3">
                            <h6 class="text-primary">{{ get_label('geolocation', 'Geolocation') }}</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ number_format($statistics['geolocation']['total_locations'] ?? 0) }}</div>
                                        <small class="text-muted">{{ get_label('total_locations', 'Total Locations') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ $statistics['geolocation']['active_users'] ?? 0 }}</div>
                                        <small class="text-muted">{{ get_label('active_users', 'Active Users') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if(\App\Services\NestKo\RoleBasedAccessService::canAccessFeature('ai_assistant'))
                        <!-- AI Stats -->
                        <div class="mb-3">
                            <h6 class="text-success">{{ get_label('ai_assistant', 'AI Assistant') }}</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ number_format($statistics['ai_assistant']['total_conversations'] ?? 0) }}</div>
                                        <small class="text-muted">{{ get_label('conversations', 'Conversations') }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <div class="fw-bold">{{ $statistics['ai_assistant']['applied_suggestions'] ?? 0 }}</div>
                                        <small class="text-muted">{{ get_label('applied_suggestions', 'Applied Suggestions') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    @endif
                </div>
            </div>

            <!-- User Permissions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">{{ get_label('your_permissions', 'Your Permissions') }}</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <i class="bx {{ $permissions['can_configure_geolocation_api'] ? 'bx-check-circle text-success' : 'bx-x-circle text-danger' }} me-2"></i>
                        <span>{{ get_label('configure_geolocation_api', 'Configure Geolocation API') }}</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <i class="bx {{ $permissions['can_configure_ai_api'] ? 'bx-check-circle text-success' : 'bx-x-circle text-danger' }} me-2"></i>
                        <span>{{ get_label('configure_ai_api', 'Configure AI API') }}</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="bx {{ $permissions['can_view_analytics'] ? 'bx-check-circle text-success' : 'bx-x-circle text-danger' }} me-2"></i>
                        <span>{{ get_label('view_analytics', 'View Analytics') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Save workspace settings
    document.getElementById('saveWorkspaceSettingsBtn').addEventListener('click', function() {
        const form = document.getElementById('workspaceSettingsForm');
        const formData = new FormData(form);
        
        // Convert FormData to JSON
        const data = {};
        for (let [key, value] of formData.entries()) {
            const keys = key.split('[').map(k => k.replace(']', ''));
            let current = data;
            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) current[keys[i]] = {};
                current = current[keys[i]];
            }
            current[keys[keys.length - 1]] = value === 'on' ? true : value;
        }
        
        fetch('{{ route('nestko.workspace.settings.update') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success('{{ get_label('settings_updated', 'Settings updated successfully') }}');
            } else {
                toastr.error(data.error || '{{ get_label('update_failed', 'Failed to update settings') }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ get_label('update_failed', 'Failed to update settings') }}');
        });
    });

    // Test API
    document.getElementById('testApiBtn').addEventListener('click', function() {
        const mapboxToken = document.getElementById('mapboxToken')?.value;
        
        if (!mapboxToken) {
            toastr.warning('{{ get_label('enter_api_key', 'Please enter an API key to test') }}');
            return;
        }
        
        fetch('{{ route('nestko.workspace.test-api') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                api_type: 'mapbox',
                api_key: mapboxToken
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const result = data.test_results.mapbox;
                if (result.status === 'success') {
                    toastr.success(`API Test Successful - ${result.response_time}`);
                } else {
                    toastr.error(`API Test Failed: ${result.message}`);
                }
            } else {
                toastr.error(data.error || '{{ get_label('api_test_failed', 'API test failed') }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ get_label('api_test_failed', 'API test failed') }}');
        });
    });
});
</script>
@endsection
