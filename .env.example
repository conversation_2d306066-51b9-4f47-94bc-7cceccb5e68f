APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:bzFxLeVAiEhh8GSCl91HGdCecRc0tbon4T8ZAwO++3k=
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=saas_dataentry
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=*******
PUSHER_APP_KEY=75e1924646380515e68b
PUSHER_APP_SECRET=83b3bfc1e29d0f444256
PUSHER_HOST=
PUSHER_PORT=
PUSHER_SCHEME=http
PUSHER_APP_CLUSTER=ap2

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

CHATIFY_ROUTES_PREFIX = 'chat'

# NestKo Geolocation Intelligence & AI Productivity Suite
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=glm4.5-air
FEATURE_NESTKO_GEO=true
FEATURE_NESTKO_AI=true
MAP_PROVIDER=mapbox
MAPBOX_ACCESS_TOKEN=
MAPBOX_STYLE=mapbox://styles/mapbox/streets-v11
LOCATION_TRACKING_INTERVAL=30000
GEOFENCE_ALERT_RADIUS=100
AI_RATE_LIMIT_PER_MINUTE=60
AI_CACHE_TTL=3600