<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('location_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('admin_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Rule name
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);

            // Location criteria
            $table->decimal('center_latitude', 10, 8);
            $table->decimal('center_longitude', 11, 8);
            $table->integer('radius_meters'); // Assignment radius in meters

            // Assignment criteria
            $table->json('role_priorities')->nullable(); // Role priority order
            $table->integer('max_assignments_per_user')->default(5);
            $table->boolean('auto_assign')->default(true);
            $table->boolean('require_skill_match')->default(false);
            $table->json('required_skills')->nullable(); // Required skills array

            // Time constraints
            $table->time('start_time')->nullable(); // Active hours start
            $table->time('end_time')->nullable(); // Active hours end
            $table->json('active_days')->nullable(); // Days of week (0-6)

            // Metadata
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['workspace_id', 'is_active']);
            $table->index(['center_latitude', 'center_longitude']);
            $table->index('auto_assign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('location_assignments');
    }
};
