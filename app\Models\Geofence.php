<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Geofence extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'created_by',
        'name',
        'description',
        'type',
        'is_active',
        'center_latitude',
        'center_longitude',
        'radius_meters',
        'polygon_coordinates',
        'alert_on_enter',
        'alert_on_exit',
        'alert_users',
        'alert_methods',
        'alert_message',
        'project_id',
        'task_id',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'center_latitude' => 'decimal:8',
        'center_longitude' => 'decimal:8',
        'polygon_coordinates' => 'array',
        'alert_on_enter' => 'boolean',
        'alert_on_exit' => 'boolean',
        'alert_users' => 'array',
        'alert_methods' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the workspace this geofence belongs to
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Get the user who created this geofence
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the associated project
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the associated task
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Scope to get active geofences
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if a point is inside this geofence
     */
    public function containsPoint($latitude, $longitude): bool
    {
        if ($this->type === 'circle') {
            return $this->isPointInCircle($latitude, $longitude);
        } elseif ($this->type === 'polygon') {
            return $this->isPointInPolygon($latitude, $longitude);
        }

        return false;
    }

    /**
     * Check if point is inside circle geofence
     */
    private function isPointInCircle($latitude, $longitude): bool
    {
        if (!$this->center_latitude || !$this->center_longitude || !$this->radius_meters) {
            return false;
        }

        $distance = $this->calculateDistance(
            $this->center_latitude,
            $this->center_longitude,
            $latitude,
            $longitude
        );

        return $distance <= $this->radius_meters;
    }

    /**
     * Check if point is inside polygon geofence
     */
    private function isPointInPolygon($latitude, $longitude): bool
    {
        if (!$this->polygon_coordinates || count($this->polygon_coordinates) < 3) {
            return false;
        }

        $vertices = $this->polygon_coordinates;
        $intersections = 0;
        $vertexCount = count($vertices);

        for ($i = 0; $i < $vertexCount; $i++) {
            $vertex1 = $vertices[$i];
            $vertex2 = $vertices[($i + 1) % $vertexCount];

            if ($this->rayIntersectsSegment($latitude, $longitude, $vertex1, $vertex2)) {
                $intersections++;
            }
        }

        return ($intersections % 2) === 1;
    }

    /**
     * Ray casting algorithm helper
     */
    private function rayIntersectsSegment($pointLat, $pointLng, $vertex1, $vertex2): bool
    {
        $lat1 = $vertex1['lat'];
        $lng1 = $vertex1['lng'];
        $lat2 = $vertex2['lat'];
        $lng2 = $vertex2['lng'];

        if ($lat1 > $pointLat !== $lat2 > $pointLat) {
            $intersectionLng = ($lng2 - $lng1) * ($pointLat - $lat1) / ($lat2 - $lat1) + $lng1;
            if ($pointLng < $intersectionLng) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate distance between two points in meters
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2): float
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $latFrom = deg2rad($lat1);
        $lonFrom = deg2rad($lng1);
        $latTo = deg2rad($lat2);
        $lonTo = deg2rad($lng2);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get users to alert for this geofence
     */
    public function getUsersToAlert(): array
    {
        if (!$this->alert_users) {
            return [];
        }

        return User::whereIn('id', $this->alert_users)->get()->toArray();
    }
}
