/*
 * Zoho Red Theme for Dashboard and Application
 * Consistent red color scheme throughout the application
 */

/* ==========================================================================
   Zoho Red Theme Colors
   ========================================================================== */
:root {
    --zoho-red: #d32f2f;
    --zoho-red-dark: #b71c1c;
    --zoho-red-light: #f44336;
    --zoho-red-hover: #c62828;
    --zoho-red-gradient: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
    --zoho-red-shadow: rgba(211, 47, 47, 0.3);
}

/* ==========================================================================
   Primary Button Theme
   ========================================================================== */
.btn-primary {
    background: var(--zoho-red-gradient) !important;
    border-color: var(--zoho-red) !important;
    color: white !important;
    transition: all 0.3s ease;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background: var(--zoho-red-hover) !important;
    border-color: var(--zoho-red-dark) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--zoho-red-shadow);
}

.btn-outline-primary {
    background: transparent !important;
    border-color: var(--zoho-red) !important;
    color: var(--zoho-red) !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background: var(--zoho-red) !important;
    border-color: var(--zoho-red) !important;
    color: white !important;
}

/* ==========================================================================
   Login and Authentication Buttons
   ========================================================================== */
.btn-login,
.btn-register,
.btn-get-started,
.btn-gradient-dark {
    background: var(--zoho-red-gradient) !important;
    border-color: var(--zoho-red) !important;
    color: white !important;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-login:hover,
.btn-register:hover,
.btn-get-started:hover,
.btn-gradient-dark:hover {
    background: var(--zoho-red-hover) !important;
    border-color: var(--zoho-red-dark) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--zoho-red-shadow);
}

/* ==========================================================================
   Form Controls and Inputs
   ========================================================================== */
.form-control:focus {
    border-color: var(--zoho-red);
    box-shadow: 0 0 0 0.2rem rgba(211, 47, 47, 0.25);
}

.form-check-input:checked {
    background-color: var(--zoho-red);
    border-color: var(--zoho-red);
}

.form-select:focus {
    border-color: var(--zoho-red);
    box-shadow: 0 0 0 0.2rem rgba(211, 47, 47, 0.25);
}

/* ==========================================================================
   Navigation and Menu
   ========================================================================== */
.nav-pills .nav-link.active {
    background-color: var(--zoho-red) !important;
}

.navbar-brand {
    color: var(--zoho-red) !important;
}

.nav-link:hover {
    color: var(--zoho-red) !important;
}

/* ==========================================================================
   Default Avatar Styling
   ========================================================================== */
.default-avatar {
    background: var(--zoho-red-gradient) !important;
    color: white !important;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar .default-avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* ==========================================================================
   Chat Widget Styling
   ========================================================================== */
.chat-widget-btn,
.chat-widget {
    background: var(--zoho-red-gradient) !important;
    border: none;
    color: white;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px var(--zoho-red-shadow);
    transition: all 0.3s ease;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chat-widget-btn:hover,
.chat-widget:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
    color: white;
}

.chat-widget-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

/* ==========================================================================
   Timer Widget Styling
   ========================================================================== */
.timer-widget,
.time-tracker {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--zoho-red);
    transition: all 0.3s ease;
}

.timer-widget:hover,
.time-tracker:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.timer-icon {
    width: 20px;
    height: 20px;
    fill: var(--zoho-red);
    margin-right: 8px;
}

.timer-display {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--zoho-red);
}

/* ==========================================================================
   Dashboard Cards and Tiles
   ========================================================================== */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
}

/* ==========================================================================
   Progress Bars and Indicators
   ========================================================================== */
.progress-bar {
    background: var(--zoho-red-gradient) !important;
}

.badge-primary {
    background-color: var(--zoho-red) !important;
}

.text-primary {
    color: var(--zoho-red) !important;
}

.bg-primary {
    background-color: var(--zoho-red) !important;
}

/* ==========================================================================
   Links and Interactive Elements
   ========================================================================== */
a {
    color: var(--zoho-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--zoho-red-hover);
    text-decoration: underline;
}

.btn-link {
    color: var(--zoho-red) !important;
}

.btn-link:hover {
    color: var(--zoho-red-hover) !important;
}

/* ==========================================================================
   Tables and Data Display
   ========================================================================== */
.table-hover tbody tr:hover {
    background-color: rgba(211, 47, 47, 0.05);
}

.table th {
    border-top: 2px solid var(--zoho-red);
    background-color: rgba(211, 47, 47, 0.05);
}

/* ==========================================================================
   Alerts and Notifications
   ========================================================================== */
.alert-primary {
    background-color: rgba(211, 47, 47, 0.1);
    border-color: var(--zoho-red);
    color: var(--zoho-red-dark);
}

.toast-success {
    background-color: var(--zoho-red) !important;
}

/* ==========================================================================
   Mobile Responsive Adjustments
   ========================================================================== */
@media (max-width: 768px) {
    .chat-widget-btn,
    .chat-widget {
        width: 48px;
        height: 48px;
        bottom: 15px;
        right: 15px;
    }
    
    .chat-widget-icon {
        width: 20px;
        height: 20px;
    }
    
    .default-avatar {
        width: 32px;
        height: 32px;
    }
    
    .default-avatar svg {
        width: 18px;
        height: 18px;
    }
    
    .timer-widget,
    .time-tracker {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */
@media (prefers-color-scheme: dark) {
    .default-avatar {
        border-color: #2d3748;
    }
    
    .timer-widget,
    .time-tracker {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .card-header {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        border-color: #718096;
    }
}

/* ==========================================================================
   Animation and Transitions
   ========================================================================== */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================================================
   Focus and Accessibility
   ========================================================================== */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--zoho-red);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .default-avatar {
        border: 3px solid #000;
    }
    
    .btn-primary {
        border: 2px solid #000;
    }
    
    .chat-widget-btn {
        border: 2px solid #fff;
    }
}
