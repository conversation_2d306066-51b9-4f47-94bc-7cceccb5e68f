<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiSuggestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'generated_by',
        'target_type',
        'target_id',
        'suggestion_type',
        'title',
        'description',
        'suggestion_data',
        'ai_model',
        'confidence_score',
        'reasoning',
        'tokens_used',
        'status',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
        'is_applied',
        'applied_at',
        'applied_changes',
        'effectiveness',
        'feedback_notes',
        'metadata',
    ];

    protected $casts = [
        'suggestion_data' => 'array',
        'applied_changes' => 'array',
        'metadata' => 'array',
        'confidence_score' => 'decimal:2',
        'is_applied' => 'boolean',
        'reviewed_at' => 'datetime',
        'applied_at' => 'datetime',
    ];

    /**
     * Get the workspace this suggestion belongs to
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Get the user who generated this suggestion
     */
    public function generator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the user who reviewed this suggestion
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the target entity (polymorphic)
     */
    public function target()
    {
        if ($this->target_type === 'task') {
            return $this->belongsTo(Task::class, 'target_id');
        } elseif ($this->target_type === 'project') {
            return $this->belongsTo(Project::class, 'target_id');
        }

        return null;
    }

    /**
     * Scope to get pending suggestions
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get accepted suggestions
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope to get applied suggestions
     */
    public function scopeApplied($query)
    {
        return $query->where('is_applied', true);
    }

    /**
     * Scope to get suggestions by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('suggestion_type', $type);
    }

    /**
     * Scope to get high confidence suggestions
     */
    public function scopeHighConfidence($query, float $threshold = 0.7)
    {
        return $query->where('confidence_score', '>=', $threshold);
    }

    /**
     * Check if suggestion is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if suggestion is accepted
     */
    public function isAccepted(): bool
    {
        return $this->status === 'accepted';
    }

    /**
     * Check if suggestion is applied
     */
    public function isApplied(): bool
    {
        return $this->is_applied;
    }

    /**
     * Get confidence level as text
     */
    public function getConfidenceLevelAttribute(): string
    {
        if ($this->confidence_score >= 0.8) {
            return 'High';
        } elseif ($this->confidence_score >= 0.6) {
            return 'Medium';
        } else {
            return 'Low';
        }
    }

    /**
     * Get confidence color for UI
     */
    public function getConfidenceColorAttribute(): string
    {
        if ($this->confidence_score >= 0.8) {
            return 'success';
        } elseif ($this->confidence_score >= 0.6) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        switch ($this->status) {
            case 'pending':
                return 'warning';
            case 'accepted':
                return 'success';
            case 'rejected':
                return 'danger';
            case 'partially_applied':
                return 'info';
            default:
                return 'secondary';
        }
    }

    /**
     * Mark suggestion as reviewed
     */
    public function markAsReviewed(int $reviewerId, string $status, string $notes = null): bool
    {
        return $this->update([
            'status' => $status,
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Mark suggestion as applied
     */
    public function markAsApplied(array $appliedChanges = []): bool
    {
        return $this->update([
            'is_applied' => true,
            'applied_at' => now(),
            'applied_changes' => $appliedChanges,
            'status' => 'accepted',
        ]);
    }

    /**
     * Get suggestion statistics for workspace
     */
    public static function getWorkspaceStats(int $workspaceId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $totalSuggestions = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $appliedSuggestions = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->where('is_applied', true)
            ->count();

        $pendingSuggestions = static::where('workspace_id', $workspaceId)
            ->where('status', 'pending')
            ->count();

        $avgConfidence = static::where('workspace_id', $workspaceId)
            ->where('created_at', '>=', $startDate)
            ->avg('confidence_score');

        return [
            'total_suggestions' => $totalSuggestions,
            'applied_suggestions' => $appliedSuggestions,
            'pending_suggestions' => $pendingSuggestions,
            'application_rate' => $totalSuggestions > 0 ? round(($appliedSuggestions / $totalSuggestions) * 100, 1) : 0,
            'avg_confidence' => round($avgConfidence ?? 0, 2),
            'period_days' => $days,
        ];
    }
}
