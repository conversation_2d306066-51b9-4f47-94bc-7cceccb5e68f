<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Services\NestKo\RoleBasedAccessService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class NestKoSettingsController extends Controller
{
    public function __construct()
    {
        // Only super admins can access these settings
        $this->middleware(function ($request, $next) {
            $user = getAuthenticatedUser();
            if (!$user || !$user->hasRole('superadmin')) {
                abort(403, 'Access denied: Super Admin access required');
            }
            return $next($request);
        });
    }

    /**
     * Display NestKo global settings for super admin
     */
    public function index(): View
    {
        $user = getAuthenticatedUser();

        // Get current global settings
        $settings = [
            'geolocation' => [
                'enabled' => env('FEATURE_NESTKO_GEO', false),
                'default_tracking_interval' => env('LOCATION_TRACKING_INTERVAL', 30000),
                'default_geofence_radius' => env('GEOFENCE_ALERT_RADIUS', 100),
                'max_location_history_days' => env('MAX_LOCATION_HISTORY_DAYS', 90),
                'enable_location_encryption' => env('ENABLE_LOCATION_ENCRYPTION', true),
                'allow_workspace_api_config' => env('ALLOW_WORKSPACE_GEO_API', true),
            ],
            'ai_assistant' => [
                'enabled' => env('FEATURE_NESTKO_AI', false),
                'openrouter_api_key' => env('OPENROUTER_API_KEY') ? '***configured***' : null,
                'default_model' => env('OPENROUTER_MODEL', 'glm4.5-air'),
                'global_rate_limit' => env('AI_RATE_LIMIT_PER_MINUTE', 60),
                'cache_ttl' => env('AI_CACHE_TTL', 3600),
                'enable_conversation_encryption' => env('ENABLE_AI_ENCRYPTION', true),
                'allow_workspace_customization' => env('ALLOW_WORKSPACE_AI_CONFIG', true),
                'cost_tracking_enabled' => env('AI_COST_TRACKING', true),
            ],
            'general' => [
                'enable_analytics' => env('NESTKO_ANALYTICS_ENABLED', true),
                'data_retention_days' => env('NESTKO_DATA_RETENTION_DAYS', 365),
                'enable_audit_logs' => env('NESTKO_AUDIT_LOGS', true),
                'max_workspaces_per_feature' => env('MAX_WORKSPACES_PER_FEATURE', 0), // 0 = unlimited
            ]
        ];

        // Get usage statistics
        $statistics = $this->getGlobalStatistics();

        return view('superadmin.nestko.settings.index', compact('settings', 'statistics'));
    }

    /**
     * Update global NestKo settings
     */
    public function updateGlobalSettings(Request $request): JsonResponse
    {
        $request->validate([
            'geolocation.enabled' => 'boolean',
            'geolocation.default_tracking_interval' => 'integer|min:5000|max:300000',
            'geolocation.default_geofence_radius' => 'integer|min:10|max:10000',
            'geolocation.max_location_history_days' => 'integer|min:1|max:365',
            'geolocation.enable_location_encryption' => 'boolean',
            'geolocation.allow_workspace_api_config' => 'boolean',

            'ai_assistant.enabled' => 'boolean',
            'ai_assistant.openrouter_api_key' => 'nullable|string',
            'ai_assistant.default_model' => 'string',
            'ai_assistant.global_rate_limit' => 'integer|min:1|max:1000',
            'ai_assistant.cache_ttl' => 'integer|min:300|max:86400',
            'ai_assistant.enable_conversation_encryption' => 'boolean',
            'ai_assistant.allow_workspace_customization' => 'boolean',
            'ai_assistant.cost_tracking_enabled' => 'boolean',

            'general.enable_analytics' => 'boolean',
            'general.data_retention_days' => 'integer|min:30|max:1095',
            'general.enable_audit_logs' => 'boolean',
            'general.max_workspaces_per_feature' => 'integer|min:0',
        ]);

        try {
            // Update environment variables
            $envUpdates = [];

            // Geolocation settings
            if ($request->has('geolocation.enabled')) {
                $envUpdates['FEATURE_NESTKO_GEO'] = $request->input('geolocation.enabled') ? 'true' : 'false';
            }
            if ($request->has('geolocation.default_tracking_interval')) {
                $envUpdates['LOCATION_TRACKING_INTERVAL'] = $request->input('geolocation.default_tracking_interval');
            }
            if ($request->has('geolocation.default_geofence_radius')) {
                $envUpdates['GEOFENCE_ALERT_RADIUS'] = $request->input('geolocation.default_geofence_radius');
            }
            if ($request->has('geolocation.max_location_history_days')) {
                $envUpdates['MAX_LOCATION_HISTORY_DAYS'] = $request->input('geolocation.max_location_history_days');
            }
            if ($request->has('geolocation.enable_location_encryption')) {
                $envUpdates['ENABLE_LOCATION_ENCRYPTION'] = $request->input('geolocation.enable_location_encryption') ? 'true' : 'false';
            }
            if ($request->has('geolocation.allow_workspace_api_config')) {
                $envUpdates['ALLOW_WORKSPACE_GEO_API'] = $request->input('geolocation.allow_workspace_api_config') ? 'true' : 'false';
            }

            // AI Assistant settings
            if ($request->has('ai_assistant.enabled')) {
                $envUpdates['FEATURE_NESTKO_AI'] = $request->input('ai_assistant.enabled') ? 'true' : 'false';
            }
            if ($request->has('ai_assistant.openrouter_api_key') && $request->input('ai_assistant.openrouter_api_key') !== '***configured***') {
                $envUpdates['OPENROUTER_API_KEY'] = $request->input('ai_assistant.openrouter_api_key');
            }
            if ($request->has('ai_assistant.default_model')) {
                $envUpdates['OPENROUTER_MODEL'] = $request->input('ai_assistant.default_model');
            }
            if ($request->has('ai_assistant.global_rate_limit')) {
                $envUpdates['AI_RATE_LIMIT_PER_MINUTE'] = $request->input('ai_assistant.global_rate_limit');
            }
            if ($request->has('ai_assistant.cache_ttl')) {
                $envUpdates['AI_CACHE_TTL'] = $request->input('ai_assistant.cache_ttl');
            }
            if ($request->has('ai_assistant.enable_conversation_encryption')) {
                $envUpdates['ENABLE_AI_ENCRYPTION'] = $request->input('ai_assistant.enable_conversation_encryption') ? 'true' : 'false';
            }
            if ($request->has('ai_assistant.allow_workspace_customization')) {
                $envUpdates['ALLOW_WORKSPACE_AI_CONFIG'] = $request->input('ai_assistant.allow_workspace_customization') ? 'true' : 'false';
            }
            if ($request->has('ai_assistant.cost_tracking_enabled')) {
                $envUpdates['AI_COST_TRACKING'] = $request->input('ai_assistant.cost_tracking_enabled') ? 'true' : 'false';
            }

            // General settings
            if ($request->has('general.enable_analytics')) {
                $envUpdates['NESTKO_ANALYTICS_ENABLED'] = $request->input('general.enable_analytics') ? 'true' : 'false';
            }
            if ($request->has('general.data_retention_days')) {
                $envUpdates['NESTKO_DATA_RETENTION_DAYS'] = $request->input('general.data_retention_days');
            }
            if ($request->has('general.enable_audit_logs')) {
                $envUpdates['NESTKO_AUDIT_LOGS'] = $request->input('general.enable_audit_logs') ? 'true' : 'false';
            }
            if ($request->has('general.max_workspaces_per_feature')) {
                $envUpdates['MAX_WORKSPACES_PER_FEATURE'] = $request->input('general.max_workspaces_per_feature');
            }

            // Update .env file
            $this->updateEnvFile($envUpdates);

            return response()->json([
                'success' => true,
                'message' => 'Global settings updated successfully',
                'updated_settings' => array_keys($envUpdates)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get global usage statistics
     */
    public function getGlobalStatistics(): array
    {
        try {
            $stats = [
                'geolocation' => [
                    'total_locations_tracked' => \DB::table('user_locations')->count(),
                    'active_workspaces' => \DB::table('user_locations')
                        ->join('users', 'user_locations.user_id', '=', 'users.id')
                        ->distinct('users.workspace_id')
                        ->count('users.workspace_id'),
                    'total_geofences' => \DB::table('geofences')->count(),
                    'locations_last_24h' => \DB::table('user_locations')
                        ->where('created_at', '>=', now()->subDay())
                        ->count(),
                ],
                'ai_assistant' => [
                    'total_conversations' => \DB::table('ai_conversations')->count(),
                    'total_suggestions' => \DB::table('ai_suggestions')->count(),
                    'active_users_last_30d' => \DB::table('ai_conversations')
                        ->where('created_at', '>=', now()->subDays(30))
                        ->distinct('user_id')
                        ->count('user_id'),
                    'total_tokens_used' => \DB::table('ai_conversations')
                        ->sum('tokens_used') ?: 0,
                ],
                'general' => [
                    'total_workspaces_using_features' => $this->getTotalWorkspacesUsingFeatures(),
                    'data_storage_size' => $this->getDataStorageSize(),
                    'last_updated' => now()->toDateTimeString(),
                ]
            ];

            return $stats;
        } catch (\Exception $e) {
            return [
                'error' => 'Unable to fetch statistics: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get workspace analytics
     */
    public function getWorkspaceAnalytics(): JsonResponse
    {
        try {
            $analytics = [
                'geolocation_usage' => \DB::table('user_locations')
                    ->join('users', 'user_locations.user_id', '=', 'users.id')
                    ->select('users.workspace_id', \DB::raw('COUNT(*) as location_count'))
                    ->groupBy('users.workspace_id')
                    ->get(),
                'ai_usage' => \DB::table('ai_conversations')
                    ->select('workspace_id', \DB::raw('COUNT(*) as conversation_count'), \DB::raw('SUM(tokens_used) as total_tokens'))
                    ->groupBy('workspace_id')
                    ->get(),
            ];

            return response()->json([
                'success' => true,
                'analytics' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test API connections
     */
    public function testApiConnections(Request $request): JsonResponse
    {
        $results = [];

        // Test OpenRouter API
        if ($request->has('test_openrouter')) {
            try {
                $apiKey = $request->input('openrouter_api_key') ?: env('OPENROUTER_API_KEY');
                if ($apiKey) {
                    $response = \Http::withHeaders([
                        'Authorization' => 'Bearer ' . $apiKey,
                        'Content-Type' => 'application/json',
                    ])->timeout(10)->get('https://openrouter.ai/api/v1/models');

                    $results['openrouter'] = [
                        'status' => $response->successful() ? 'success' : 'failed',
                        'message' => $response->successful() ? 'API connection successful' : 'API connection failed',
                        'response_time' => $response->transferStats ? round($response->transferStats->getTransferTime() * 1000) . 'ms' : 'N/A'
                    ];
                } else {
                    $results['openrouter'] = [
                        'status' => 'failed',
                        'message' => 'API key not provided'
                    ];
                }
            } catch (\Exception $e) {
                $results['openrouter'] = [
                    'status' => 'failed',
                    'message' => 'Connection error: ' . $e->getMessage()
                ];
            }
        }

        // Test Mapbox API
        if ($request->has('test_mapbox')) {
            try {
                $mapboxToken = env('MAPBOX_ACCESS_TOKEN');
                if ($mapboxToken) {
                    $response = \Http::timeout(10)->get("https://api.mapbox.com/geocoding/v5/mapbox.places/test.json?access_token={$mapboxToken}");

                    $results['mapbox'] = [
                        'status' => $response->successful() ? 'success' : 'failed',
                        'message' => $response->successful() ? 'Mapbox API connection successful' : 'Mapbox API connection failed',
                        'response_time' => $response->transferStats ? round($response->transferStats->getTransferTime() * 1000) . 'ms' : 'N/A'
                    ];
                } else {
                    $results['mapbox'] = [
                        'status' => 'failed',
                        'message' => 'Mapbox access token not configured'
                    ];
                }
            } catch (\Exception $e) {
                $results['mapbox'] = [
                    'status' => 'failed',
                    'message' => 'Connection error: ' . $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'test_results' => $results
        ]);
    }

    /**
     * Update environment file
     */
    protected function updateEnvFile(array $updates): void
    {
        $envPath = base_path('.env');
        $envContent = file_get_contents($envPath);

        foreach ($updates as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envPath, $envContent);
    }

    /**
     * Get total workspaces using NestKo features
     */
    protected function getTotalWorkspacesUsingFeatures(): int
    {
        $geoWorkspaces = \DB::table('user_locations')
            ->join('users', 'user_locations.user_id', '=', 'users.id')
            ->distinct('users.workspace_id')
            ->count('users.workspace_id');

        $aiWorkspaces = \DB::table('ai_conversations')
            ->distinct('workspace_id')
            ->count('workspace_id');

        return max($geoWorkspaces, $aiWorkspaces);
    }

    /**
     * Get approximate data storage size
     */
    protected function getDataStorageSize(): string
    {
        try {
            $locationSize = \DB::table('user_locations')->count() * 0.5; // Approximate KB per record
            $conversationSize = \DB::table('ai_conversations')->count() * 2; // Approximate KB per record
            $totalKB = $locationSize + $conversationSize;

            if ($totalKB > 1024 * 1024) {
                return round($totalKB / (1024 * 1024), 2) . ' GB';
            } elseif ($totalKB > 1024) {
                return round($totalKB / 1024, 2) . ' MB';
            } else {
                return round($totalKB, 2) . ' KB';
            }
        } catch (\Exception $e) {
            return 'Unable to calculate';
        }
    }
}
