<?php

namespace App\Http\Controllers\NestKo;

use App\Http\Controllers\Controller;
use App\Services\NestKo\RoleBasedAccessService;
use App\Models\Workspace;
use App\Models\WorkspaceSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class WorkspaceSettingsController extends Controller
{
    public function __construct()
    {
        // Only workspace admins and managers can access these settings
        $this->middleware(function ($request, $next) {
            $user = getAuthenticatedUser();
            if (!$user || !RoleBasedAccessService::canManageWorkspaceSettings($user)) {
                abort(403, 'Access denied: Workspace admin access required');
            }
            return $next($request);
        });
    }

    /**
     * Display workspace NestKo settings
     */
    public function index(): View
    {
        $user = getAuthenticatedUser();
        $workspaceId = getWorkspaceId();

        if (!$workspaceId) {
            abort(404, 'Workspace not found');
        }

        // Get workspace settings
        $settings = $this->getWorkspaceSettings($workspaceId);

        // Get workspace statistics
        $statistics = $this->getWorkspaceStatistics($workspaceId);

        // Get user permissions
        $permissions = [
            'can_configure_geolocation_api' => RoleBasedAccessService::canAccessAPI('geolocation', $user),
            'can_configure_ai_api' => RoleBasedAccessService::canAccessAPI('ai_assistant', $user),
            'can_view_analytics' => RoleBasedAccessService::canViewAnalytics('geolocation', $user) ||
                                   RoleBasedAccessService::canViewAnalytics('ai_assistant', $user),
        ];

        return view('nestko.workspace.settings.index', compact('settings', 'statistics', 'permissions'));
    }

    /**
     * Update workspace settings
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $workspaceId = getWorkspaceId();
        $user = getAuthenticatedUser();

        if (!$workspaceId) {
            return response()->json(['error' => 'Workspace not found'], 404);
        }

        $request->validate([
            'geolocation.enabled' => 'boolean',
            'geolocation.tracking_interval' => 'integer|min:5000|max:300000',
            'geolocation.geofence_radius' => 'integer|min:10|max:10000',
            'geolocation.mapbox_access_token' => 'nullable|string',
            'geolocation.map_style' => 'nullable|string',
            'geolocation.enable_alerts' => 'boolean',
            'geolocation.alert_email' => 'nullable|email',
            'geolocation.privacy_mode' => 'boolean',

            'ai_assistant.enabled' => 'boolean',
            'ai_assistant.custom_model' => 'nullable|string',
            'ai_assistant.rate_limit' => 'integer|min:1|max:200',
            'ai_assistant.enable_voice_input' => 'boolean',
            'ai_assistant.enable_suggestions' => 'boolean',
            'ai_assistant.conversation_retention_days' => 'integer|min:1|max:365',
        ]);

        try {
            $settingsToUpdate = [];

            // Geolocation settings
            if ($request->has('geolocation')) {
                $geoSettings = $request->input('geolocation');

                foreach ($geoSettings as $key => $value) {
                    if ($key === 'mapbox_access_token' && !RoleBasedAccessService::canAccessAPI('geolocation', $user)) {
                        continue; // Skip if user can't configure API
                    }

                    $settingsToUpdate["geolocation.{$key}"] = $value;
                }
            }

            // AI Assistant settings
            if ($request->has('ai_assistant')) {
                $aiSettings = $request->input('ai_assistant');

                foreach ($aiSettings as $key => $value) {
                    $settingsToUpdate["ai_assistant.{$key}"] = $value;
                }
            }

            // Update settings in database
            foreach ($settingsToUpdate as $key => $value) {
                WorkspaceSetting::updateOrCreate(
                    [
                        'workspace_id' => $workspaceId,
                        'key' => $key
                    ],
                    [
                        'value' => is_bool($value) ? ($value ? '1' : '0') : $value,
                        'updated_by' => $user->id
                    ]
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'Workspace settings updated successfully',
                'updated_settings' => array_keys($settingsToUpdate)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test workspace API configurations
     */
    public function testApiConfiguration(Request $request): JsonResponse
    {
        $user = getAuthenticatedUser();
        $workspaceId = getWorkspaceId();

        $request->validate([
            'api_type' => 'required|in:mapbox,openrouter',
            'api_key' => 'required|string',
        ]);

        $results = [];

        try {
            if ($request->api_type === 'mapbox' && RoleBasedAccessService::canAccessAPI('geolocation', $user)) {
                $response = \Http::timeout(10)->get("https://api.mapbox.com/geocoding/v5/mapbox.places/test.json?access_token={$request->api_key}");

                $results['mapbox'] = [
                    'status' => $response->successful() ? 'success' : 'failed',
                    'message' => $response->successful() ? 'Mapbox API connection successful' : 'Invalid API key or connection failed',
                    'response_time' => $response->transferStats ? round($response->transferStats->getTransferTime() * 1000) . 'ms' : 'N/A'
                ];
            } elseif ($request->api_type === 'openrouter' && RoleBasedAccessService::canAccessAPI('ai_assistant', $user)) {
                $response = \Http::withHeaders([
                    'Authorization' => 'Bearer ' . $request->api_key,
                    'Content-Type' => 'application/json',
                ])->timeout(10)->get('https://openrouter.ai/api/v1/models');

                $results['openrouter'] = [
                    'status' => $response->successful() ? 'success' : 'failed',
                    'message' => $response->successful() ? 'OpenRouter API connection successful' : 'Invalid API key or connection failed',
                    'response_time' => $response->transferStats ? round($response->transferStats->getTransferTime() * 1000) . 'ms' : 'N/A'
                ];
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Unauthorized to test this API configuration'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'test_results' => $results
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'API test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get workspace settings
     */
    protected function getWorkspaceSettings(int $workspaceId): array
    {
        $settings = WorkspaceSetting::where('workspace_id', $workspaceId)->get();

        $defaultSettings = [
            'geolocation' => [
                'enabled' => env('FEATURE_NESTKO_GEO', false),
                'tracking_interval' => env('LOCATION_TRACKING_INTERVAL', 30000),
                'geofence_radius' => env('GEOFENCE_ALERT_RADIUS', 100),
                'mapbox_access_token' => null,
                'map_style' => 'mapbox://styles/mapbox/streets-v11',
                'enable_alerts' => true,
                'alert_email' => null,
                'privacy_mode' => false,
            ],
            'ai_assistant' => [
                'enabled' => env('FEATURE_NESTKO_AI', false),
                'custom_model' => env('OPENROUTER_MODEL', 'glm4.5-air'),
                'rate_limit' => env('AI_RATE_LIMIT_PER_MINUTE', 60),
                'enable_voice_input' => true,
                'enable_suggestions' => true,
                'conversation_retention_days' => 90,
            ]
        ];

        // Override with workspace-specific settings
        foreach ($settings as $setting) {
            $keys = explode('.', $setting->key);
            if (count($keys) === 2) {
                $defaultSettings[$keys[0]][$keys[1]] = $setting->value === '1' ? true : ($setting->value === '0' ? false : $setting->value);
            }
        }

        return $defaultSettings;
    }

    /**
     * Get workspace statistics
     */
    protected function getWorkspaceStatistics(int $workspaceId): array
    {
        try {
            return [
                'geolocation' => [
                    'total_locations' => \DB::table('user_locations')
                        ->join('users', 'user_locations.user_id', '=', 'users.id')
                        ->where('users.workspace_id', $workspaceId)
                        ->count(),
                    'active_users' => \DB::table('user_locations')
                        ->join('users', 'user_locations.user_id', '=', 'users.id')
                        ->where('users.workspace_id', $workspaceId)
                        ->where('user_locations.created_at', '>=', now()->subDays(7))
                        ->distinct('user_locations.user_id')
                        ->count('user_locations.user_id'),
                    'total_geofences' => \DB::table('geofences')
                        ->where('workspace_id', $workspaceId)
                        ->count(),
                ],
                'ai_assistant' => [
                    'total_conversations' => \DB::table('ai_conversations')
                        ->where('workspace_id', $workspaceId)
                        ->count(),
                    'active_users' => \DB::table('ai_conversations')
                        ->where('workspace_id', $workspaceId)
                        ->where('created_at', '>=', now()->subDays(30))
                        ->distinct('user_id')
                        ->count('user_id'),
                    'total_suggestions' => \DB::table('ai_suggestions')
                        ->where('workspace_id', $workspaceId)
                        ->count(),
                    'applied_suggestions' => \DB::table('ai_suggestions')
                        ->where('workspace_id', $workspaceId)
                        ->where('is_applied', true)
                        ->count(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Unable to fetch statistics: ' . $e->getMessage()
            ];
        }
    }
}
