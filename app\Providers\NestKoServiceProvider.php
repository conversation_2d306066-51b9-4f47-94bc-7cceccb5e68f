<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\NestKo\OpenRouterService;
use App\Services\NestKo\LocationTrackingService;
use App\Services\NestKo\GeofenceAlertService;

class NestKoServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register NestKo services as singletons
        $this->app->singleton(OpenRouterService::class, function ($app) {
            return new OpenRouterService();
        });

        $this->app->singleton(LocationTrackingService::class, function ($app) {
            return new LocationTrackingService($app->make(GeofenceAlertService::class));
        });

        $this->app->singleton(GeofenceAlertService::class, function ($app) {
            return new GeofenceAlertService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load NestKo configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/nestko.php', 'nestko'
        );

        // Publish configuration if needed
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/nestko.php' => config_path('nestko.php'),
            ], 'nestko-config');
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            OpenRouterService::class,
            LocationTrackingService::class,
            GeofenceAlertService::class,
        ];
    }
}
