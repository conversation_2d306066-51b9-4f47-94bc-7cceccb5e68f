<?php

return [
    /*
    |--------------------------------------------------------------------------
    | NestKo Feature Flags
    |--------------------------------------------------------------------------
    |
    | This configuration file manages feature flags for NestKo's new
    | Geolocation Intelligence and AI Productivity Suite features.
    |
    */

    'features' => [
        'geolocation' => [
            'enabled' => env('FEATURE_NESTKO_GEO', false),
            'name' => 'Geolocation Intelligence',
            'description' => 'Location-based task assignment, live tracking, and proximity alerts',
            'icon' => 'bx bx-map',
            'modules' => [
                'location_tracking' => [
                    'enabled' => true,
                    'interval' => env('LOCATION_TRACKING_INTERVAL', 30000), // milliseconds
                ],
                'task_assignment' => [
                    'enabled' => true,
                    'auto_assign' => true,
                ],
                'live_map' => [
                    'enabled' => true,
                    'provider' => env('MAP_PROVIDER', 'google'),
                ],
                'proximity_alerts' => [
                    'enabled' => true,
                    'default_radius' => env('GEOFENCE_ALERT_RADIUS', 100), // meters
                ],
                'route_optimization' => [
                    'enabled' => true,
                    'ai_powered' => true,
                ],
                'location_history' => [
                    'enabled' => true,
                    'retention_days' => 90,
                ],
            ],
        ],

        'ai_assistant' => [
            'enabled' => env('FEATURE_NESTKO_AI', false),
            'name' => 'AI Productivity Suite',
            'description' => 'AI-powered project assistance, task summarization, and smart suggestions',
            'icon' => 'bx bx-brain',
            'modules' => [
                'project_assistant' => [
                    'enabled' => true,
                    'per_member_chat' => true,
                ],
                'task_summarizer' => [
                    'enabled' => true,
                    'auto_summarize' => false,
                ],
                'smart_suggestions' => [
                    'enabled' => true,
                    'suggest_deadlines' => true,
                    'suggest_assignee' => true,
                    'suggest_order' => true,
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | OpenRouter AI Configuration
    |--------------------------------------------------------------------------
    */
    'openrouter' => [
        'api_key' => env('OPENROUTER_API_KEY'),
        'model' => env('OPENROUTER_MODEL', 'glm4.5-air'),
        'base_url' => 'https://openrouter.ai/api/v1',
        'rate_limit' => [
            'per_minute' => env('AI_RATE_LIMIT_PER_MINUTE', 60),
            'per_hour' => env('AI_RATE_LIMIT_PER_HOUR', 1000),
        ],
        'cache' => [
            'enabled' => true,
            'ttl' => env('AI_CACHE_TTL', 3600), // seconds
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Map Configuration
    |--------------------------------------------------------------------------
    */
    'maps' => [
        'provider' => env('MAP_PROVIDER', 'mapbox'),
        'mapbox' => [
            'access_token' => env('MAPBOX_ACCESS_TOKEN'),
            'style' => env('MAPBOX_STYLE', 'mapbox://styles/mapbox/streets-v11'),
        ],
        'google' => [
            'api_key' => env('GOOGLE_MAPS_API_KEY'),
            'libraries' => ['places', 'geometry'],
        ],
        'default_center' => [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ],
        'default_zoom' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | Geolocation Settings
    |--------------------------------------------------------------------------
    */
    'geolocation' => [
        'tracking' => [
            'enabled' => true,
            'interval' => env('LOCATION_TRACKING_INTERVAL', 30000),
            'high_accuracy' => true,
            'timeout' => 10000,
            'maximum_age' => 60000,
        ],
        'geofencing' => [
            'default_radius' => env('GEOFENCE_ALERT_RADIUS', 100),
            'max_radius' => 5000,
            'min_radius' => 10,
        ],
        'privacy' => [
            'require_consent' => true,
            'anonymize_after_days' => 30,
            'encrypt_coordinates' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Responsive Breakpoints
    |--------------------------------------------------------------------------
    */
    'responsive' => [
        'breakpoints' => [
            'mobile' => 480,
            'tablet' => 1024,
            'desktop' => 1025,
        ],
        'map_scaling' => [
            'mobile' => [
                'height' => '300px',
                'controls' => 'minimal',
            ],
            'tablet' => [
                'height' => '400px',
                'controls' => 'standard',
            ],
            'desktop' => [
                'height' => '500px',
                'controls' => 'full',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'location_data_encryption' => true,
        'ai_conversation_encryption' => true,
        'audit_trail' => true,
        'data_retention' => [
            'location_history' => 90, // days
            'ai_conversations' => 365, // days
            'audit_logs' => 730, // days
        ],
    ],
];
