/**
 * Mobile Enhancement JavaScript for Front-End
 * Handles sticky navbar, mobile optimizations, and responsive behavior
 */

document.addEventListener('DOMContentLoaded', function() {
    initMobileEnhancements();
});

function initMobileEnhancements() {
    handleStickyNavbar();
    setupToTopButtonOnMobile();
    optimizeForMobile();
    handleMobileNavigation();
    addTouchFeedback();
}

/**
 * Handle sticky navbar behavior on mobile
 */
function handleStickyNavbar() {
    const navbar = document.querySelector('.navbar.fixed-top');
    if (!navbar) return;

    let lastScrollTop = 0;
    let isScrolling = false;

    window.addEventListener('scroll', function() {
        if (!isScrolling) {
            window.requestAnimationFrame(function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                // Add scrolled class for styling
                if (scrollTop > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }

                // On mobile, keep navbar always visible but adjust styling
                if (window.innerWidth <= 768) {
                    // Always keep navbar visible and accessible
                    navbar.style.transform = 'translateY(0)';
                    navbar.style.opacity = '1';

                    // Add subtle background change on scroll
                    if (scrollTop > 50) {
                        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
                    } else {
                        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                        navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
                    }
                }

                lastScrollTop = scrollTop;
                isScrolling = false;
            });
        }
        isScrolling = true;
    }, { passive: true });
}

/**
 * Setup to-top button for mobile devices
 */
function setupToTopButtonOnMobile() {
    const toTopButtons = document.querySelectorAll(
        '.to-top, .back-to-top, .scroll-to-top, [class*="to-top"], [class*="back-to-top"], [class*="scroll-to-top"], #backToTopBtn'
    );

    toTopButtons.forEach(button => {
        // Ensure button is visible on mobile
        if (window.innerWidth <= 768) {
            button.style.display = 'block';
            button.style.position = 'fixed';
            button.style.bottom = '20px';
            button.style.right = '20px';
            button.style.zIndex = '1000';
        }

        // Add click handler if not already present
        if (!button.hasAttribute('data-mobile-enhanced')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
            button.setAttribute('data-mobile-enhanced', 'true');
        }
    });

    // Setup scroll listener for show/hide
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            toTopButtons.forEach(button => {
                if (scrollTop > 300) {
                    button.classList.add('show');
                } else {
                    button.classList.remove('show');
                }
            });
        }, 10);
    }, { passive: true });
}

/**
 * Mobile-specific optimizations
 */
function optimizeForMobile() {
    if (window.innerWidth <= 768) {
        // Prevent zoom on input focus (iOS)
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.style.fontSize !== '16px') {
                input.style.fontSize = '16px';
            }
        });

        // Add mobile classes to body
        document.body.classList.add('mobile-device');

        // Optimize images for mobile
        optimizeImagesForMobile();

        // Handle virtual keyboard
        handleVirtualKeyboard();
    }
}

/**
 * Optimize images for mobile
 */
function optimizeImagesForMobile() {
    // Hide floating hero images on mobile
    const floatingImages = document.querySelectorAll('.floating-card.top-right, .floating-card.bottom-left');
    floatingImages.forEach(img => {
        img.style.display = 'none';
    });

    // Make main hero image responsive
    const mainHeroImage = document.querySelector('.main-image-container img');
    if (mainHeroImage) {
        mainHeroImage.style.width = '100%';
        mainHeroImage.style.height = 'auto';
        mainHeroImage.style.minHeight = '250px';
        mainHeroImage.style.objectFit = 'cover';
    }
}

/**
 * Handle mobile navigation
 */
function handleMobileNavigation() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            // Add animation classes
            navbarCollapse.classList.toggle('show');
            
            // Animate hamburger icon
            const bars = navbarToggler.querySelectorAll('.navbar-toggler-bar');
            bars.forEach((bar, index) => {
                if (navbarCollapse.classList.contains('show')) {
                    if (index === 0) bar.style.transform = 'rotate(45deg) translate(5px, 5px)';
                    if (index === 1) bar.style.opacity = '0';
                    if (index === 2) bar.style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    bar.style.transform = '';
                    bar.style.opacity = '';
                }
            });
        });

        // Close mobile menu when clicking on links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    navbarCollapse.classList.remove('show');
                    const bars = navbarToggler.querySelectorAll('.navbar-toggler-bar');
                    bars.forEach(bar => {
                        bar.style.transform = '';
                        bar.style.opacity = '';
                    });
                }
            });
        });
    }
}

/**
 * Add touch feedback for mobile interactions
 */
function addTouchFeedback() {
    const touchElements = document.querySelectorAll('.btn, .nav-link, .card, .touch-target');
    
    touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
            this.style.opacity = '0.8';
        }, { passive: true });

        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
                this.style.opacity = '';
            }, 150);
        }, { passive: true });

        element.addEventListener('touchcancel', function() {
            this.style.transform = '';
            this.style.opacity = '';
        }, { passive: true });
    });
}

/**
 * Handle virtual keyboard on mobile
 */
function handleVirtualKeyboard() {
    let initialViewportHeight = window.innerHeight;

    window.addEventListener('resize', function() {
        const currentHeight = window.innerHeight;
        const heightDifference = initialViewportHeight - currentHeight;

        if (heightDifference > 150) {
            // Keyboard is likely open
            document.body.classList.add('keyboard-open');
        } else {
            // Keyboard is likely closed
            document.body.classList.remove('keyboard-open');
        }
    });
}

/**
 * Smooth scroll for anchor links
 */
function initSmoothScroll() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - (window.innerWidth <= 768 ? 80 : 100);
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Initialize default avatars with SVG
 */
function initDefaultAvatars() {
    const avatarElements = document.querySelectorAll('.avatar, .user-avatar, .profile-avatar');
    
    avatarElements.forEach(avatar => {
        if (!avatar.querySelector('img') && !avatar.querySelector('svg')) {
            const svgIcon = document.createElement('div');
            svgIcon.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="default-avatar-icon">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
            `;
            avatar.appendChild(svgIcon);
            avatar.classList.add('default-avatar');
        }
    });
}

/**
 * Initialize chat widgets with SVG icons
 */
function initChatWidgets() {
    const chatButtons = document.querySelectorAll('.chat-widget, .chat-btn, .support-chat');
    
    chatButtons.forEach(button => {
        if (!button.querySelector('svg')) {
            const svgIcon = document.createElement('div');
            svgIcon.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="chat-widget-icon">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
            `;
            button.appendChild(svgIcon);
            button.classList.add('chat-widget-btn');
        }
    });
}

/**
 * Initialize timer widgets with SVG icons
 */
function initTimerWidgets() {
    const timerElements = document.querySelectorAll('.timer, .time-tracker, .timer-widget');
    
    timerElements.forEach(timer => {
        if (!timer.querySelector('svg')) {
            const svgIcon = document.createElement('div');
            svgIcon.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="timer-icon">
                    <path d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.03-6.61l1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61zM12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"/>
                </svg>
            `;
            timer.appendChild(svgIcon);
        }
    });
}

/**
 * Handle orientation change
 */
function handleOrientationChange() {
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            // Recalculate viewport height
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
            
            // Re-optimize for new orientation
            optimizeForMobile();
        }, 100);
    });
}

/**
 * Initialize all mobile enhancements
 */
function initAllEnhancements() {
    initSmoothScroll();
    initDefaultAvatars();
    initChatWidgets();
    initTimerWidgets();
    handleOrientationChange();
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', initAllEnhancements);

// Re-initialize on window resize
window.addEventListener('resize', function() {
    setupToTopButtonOnMobile();
    optimizeForMobile();
});

// Set initial viewport height
const vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);
