<?php

namespace App\Http\Controllers\NestKo;

use App\Http\Controllers\Controller;
use App\Services\NestKo\LocationTrackingService;
use App\Services\NestKo\GeofenceAlertService;
use App\Services\NestKo\RoleBasedAccessService;
use App\Models\UserLocation;
use App\Models\Geofence;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;

class GeolocationController extends Controller
{
    protected $locationService;
    protected $geofenceAlertService;

    public function __construct(
        LocationTrackingService $locationService,
        GeofenceAlertService $geofenceAlertService
    ) {
        $this->locationService = $locationService;
        $this->geofenceAlertService = $geofenceAlertService;

        // Check role-based feature access
        $this->middleware(function ($request, $next) {
            $user = getAuthenticatedUser();
            RoleBasedAccessService::validateFeatureAccess('geolocation', null, $user);
            return $next($request);
        });
    }

    /**
     * Display the main geolocation map view
     */
    public function index(): View
    {
        $workspaceId = getWorkspaceId();

        // Get current user locations
        $userLocations = UserLocation::with('user')
            ->where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->get();

        // Get active geofences
        $geofences = Geofence::where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->get();

        return view('nestko.geolocation.index', compact('userLocations', 'geofences'));
    }

    /**
     * Update user's current location
     */
    public function updateLocation(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'accuracy' => 'nullable|numeric|min:0',
            'altitude' => 'nullable|numeric',
            'heading' => 'nullable|numeric|between:0,360',
            'speed' => 'nullable|numeric|min:0',
            'address' => 'nullable|string|max:255',
        ]);

        try {
            $user = getAuthenticatedUser();
            $workspaceId = getWorkspaceId();

            $location = $this->locationService->updateUserLocation(
                $user->id,
                $workspaceId,
                $request->latitude,
                $request->longitude,
                [
                    'accuracy' => $request->accuracy,
                    'altitude' => $request->altitude,
                    'heading' => $request->heading,
                    'speed' => $request->speed,
                    'address' => $request->address,
                    'metadata' => $request->metadata,
                    'device_id' => $request->device_id,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Location updated successfully',
                'location' => $location
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current locations for map display
     */
    public function getCurrentLocations(): JsonResponse
    {
        $workspaceId = getWorkspaceId();

        $locations = UserLocation::with(['user:id,first_name,last_name,photo'])
            ->where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->get()
            ->map(function ($location) {
                return [
                    'id' => $location->id,
                    'user' => [
                        'id' => $location->user->id,
                        'name' => $location->user->first_name . ' ' . $location->user->last_name,
                        'photo' => $location->user->photo ? asset('storage/' . $location->user->photo) : null,
                    ],
                    'latitude' => $location->latitude,
                    'longitude' => $location->longitude,
                    'accuracy' => $location->accuracy,
                    'address' => $location->address,
                    'recorded_at' => $location->recorded_at->toISOString(),
                    'is_stale' => $location->isStale(),
                ];
            });

        return response()->json($locations);
    }

    /**
     * Get geofences for map display
     */
    public function getGeofences(): JsonResponse
    {
        $workspaceId = getWorkspaceId();

        $geofences = Geofence::where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->get()
            ->map(function ($geofence) {
                return [
                    'id' => $geofence->id,
                    'name' => $geofence->name,
                    'description' => $geofence->description,
                    'type' => $geofence->type,
                    'center_latitude' => $geofence->center_latitude,
                    'center_longitude' => $geofence->center_longitude,
                    'radius_meters' => $geofence->radius_meters,
                    'polygon_coordinates' => $geofence->polygon_coordinates,
                    'alert_on_enter' => $geofence->alert_on_enter,
                    'alert_on_exit' => $geofence->alert_on_exit,
                ];
            });

        return response()->json($geofences);
    }

    /**
     * Get location history for a user
     */
    public function getLocationHistory(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'limit' => 'nullable|integer|min:1|max:1000',
        ]);

        $user = getAuthenticatedUser();
        $workspaceId = getWorkspaceId();
        $userId = $request->user_id ?? $user->id;

        // Check if user can view other users' location history
        if ($userId !== $user->id && !$user->can('manage_users')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;
        $limit = $request->limit ?? 100;

        $history = $this->locationService->getLocationHistory(
            $userId,
            $workspaceId,
            $startDate,
            $endDate,
            $limit
        );

        return response()->json($history);
    }

    /**
     * Find users near a location
     */
    public function findNearbyUsers(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius_meters' => 'required|integer|min:1|max:50000',
        ]);

        $workspaceId = getWorkspaceId();
        $user = getAuthenticatedUser();

        $nearbyUsers = $this->locationService->getUsersNearLocation(
            $workspaceId,
            $request->latitude,
            $request->longitude,
            $request->radius_meters,
            [$user->id] // Exclude current user
        );

        $result = $nearbyUsers->map(function ($location) {
            return [
                'user' => [
                    'id' => $location->user->id,
                    'name' => $location->user->first_name . ' ' . $location->user->last_name,
                    'photo' => $location->user->photo ? asset('storage/' . $location->user->photo) : null,
                ],
                'latitude' => $location->latitude,
                'longitude' => $location->longitude,
                'distance_meters' => round($location->distance * 1000), // Convert km to meters
                'recorded_at' => $location->recorded_at->toISOString(),
            ];
        });

        return response()->json($result);
    }
}
